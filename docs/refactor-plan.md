# 类调用方式重构计划

## 1. 背景与目标

根据项目要求，需要筛查全项目中的类调用方式，避免使用 `$this->xxx` 的方式调用其他类，除非有必须情况，比如需要保持类的运行时存储，否则调用时再重新 new，防止污染。

## 2. 项目架构分析

项目是一个使用 ThinkPHP 8.0 框架的多模块应用程序，包含以下模块：
- index（前台门户）
- api（后台API）
- common（公共组件，包含服务、仓库和模型）

项目使用了仓库模式（Repository Pattern）和服务层（Service Layer）架构。

## 3. 需要修改的调用模式

### 3.1 控制器中的类调用模式

```php
// app/index/controller/Index.php
class Index extends Base {
    protected $articleService;
    protected $articleRepository;
    
    public function __construct(App $app) {
        parent::__construct($app);
        $this->articleService = new ArticleService();
        $this->articleRepository = new ArticleRepository();
    }
    
    public function index() {
        $categories = $this->categoryRepository->findAll(true);
    }
}
```

### 3.2 服务类中的类调用模式

```php
// app/common/service/ArticleService.php
class ArticleService {
    private ArticleRepository $articleRepository;
    
    public function __construct() {
        $this->articleRepository = new ArticleRepository();
    }
    
    public function getById(int $id): ?ArticleBean {
        return $this->articleRepository->findById($id);
    }
}
```

### 3.3 中间件中的类调用模式

```php
// app/api/middleware/AuthMiddleware.php
class AuthMiddleware {
    public function handle(Request $request, Closure $next): Response {
        $authService = new AdminAuthService();
        
        if (!$authService->isLogin()) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
    }
}
```

## 4. 需要保留的调用模式

以下 `$this->xxx` 调用应该保留：

1. 框架核心组件：
   - `$this->request`（BaseController 中定义）
   - `$this->app`（BaseController 中定义）

2. 需要保持状态的组件：
   - 缓存管理器
   - 会话管理器
   - 配置管理器

3. 单例模式的组件：
   - 框架内置的单例组件

## 5. 修改文件列表

### 5.1 控制器文件

1. app/index/controller/Index.php
   - 移除类属性定义：`protected $articleService` 等
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

2. app/api/controller/ArticleController.php
   - 移除类属性定义：`private ArticleService $articleService` 等
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

3. app/api/controller/UserController.php
   - 移除类属性定义：`protected AdminUserService $userService`
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

### 5.2 服务类文件

1. app/common/service/ArticleService.php
   - 移除类属性定义：`private ArticleRepository $articleRepository` 等
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

2. app/common/service/AdminUserService.php
   - 移除类属性定义：`protected AdminUserRepository $userRepository` 等
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

3. app/common/service/ArticleCategoryService.php
   - 移除类属性定义：`protected ArticleCategoryRepository $repository`
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

4. app/common/service/AdminAuthService.php
   - 移除类属性定义：`protected AdminUserRepository $userRepository` 等
   - 移除构造函数中的实例化代码
   - 将方法中的 `$this->xxx` 调用改为 `(new XxxClass())->method()`

### 5.3 中间件文件

1. app/api/middleware/AuthMiddleware.php
   - 已经使用直接实例化方式，不需要修改

2. app/api/middleware/RoleMiddleware.php
   - 已经使用直接实例化方式，不需要修改

## 6. 修改示例

### 6.1 控制器修改示例

```php
// 修改前
class Index extends Base {
    protected $articleService;
    
    public function __construct(App $app) {
        parent::__construct($app);
        $this->articleService = new ArticleService();
    }
    
    public function index() {
        $article = $this->articleService->getById($id);
    }
}

// 修改后
class Index extends Base {
    public function __construct(App $app) {
        parent::__construct($app);
    }
    
    public function index() {
        $article = (new ArticleService())->getById($id);
    }
}
```

### 6.2 服务类修改示例

```php
// 修改前
class ArticleService {
    private ArticleRepository $articleRepository;
    
    public function __construct() {
        $this->articleRepository = new ArticleRepository();
    }
    
    public function getById(int $id): ?ArticleBean {
        return $this->articleRepository->findById($id);
    }
}

// 修改后
class ArticleService {
    public function getById(int $id): ?ArticleBean {
        return (new ArticleRepository())->findById($id);
    }
}
```

## 7. 性能优化考虑

对于频繁实例化的情况，可以使用本地变量缓存实例：

```php
// 优化前
public function batchProcess(array $ids) {
    foreach ($ids as $id) {
        (new ArticleRepository())->process($id);
    }
}

// 优化后
public function batchProcess(array $ids) {
    $repository = new ArticleRepository();
    foreach ($ids as $id) {
        $repository->process($id);
    }
}
```

## 8. 修改优先级和顺序

1. 首先修改控制器中的服务和仓库调用
2. 然后修改服务类中的仓库调用
3. 最后检查中间件和其他可能的文件

## 9. 测试策略

修改完成后，应该进行以下测试：
1. 单元测试（如果有）
2. 功能测试，确保各个页面和API正常工作
3. 性能测试，确保修改不会带来明显的性能下降
