<?php
/**
 * 综合测试运行脚本
 * 运行所有测试并生成综合报告
 */

class TestRunner
{
    private $baseUrl;
    private $projectRoot;
    private $results = [];
    
    public function __construct($baseUrl = 'http://news.test.jijiaox.com', $projectRoot = null)
    {
        $this->baseUrl = $baseUrl;
        $this->projectRoot = $projectRoot ?: dirname(__DIR__, 2);
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== 类调用方式重构 - 综合测试报告 ===\n";
        echo "测试时间: " . date('Y-m-d H:i:s') . "\n";
        echo "测试环境: {$this->baseUrl}\n";
        echo "项目路径: {$this->projectRoot}\n\n";
        
        // 1. 代码质量检查
        echo "第一阶段：代码质量检查\n";
        echo str_repeat('-', 40) . "\n";
        $this->runCodeQualityCheck();
        
        // 2. 功能测试
        echo "\n第二阶段：功能测试\n";
        echo str_repeat('-', 40) . "\n";
        $this->runFunctionalTests();
        
        // 3. 性能测试
        echo "\n第三阶段：性能测试\n";
        echo str_repeat('-', 40) . "\n";
        $this->runPerformanceTests();
        
        // 4. 生成综合报告
        echo "\n第四阶段：生成综合报告\n";
        echo str_repeat('-', 40) . "\n";
        $this->generateComprehensiveReport();
    }
    
    /**
     * 运行代码质量检查
     */
    private function runCodeQualityCheck()
    {
        $startTime = microtime(true);
        
        // 执行代码质量检查
        ob_start();
        include __DIR__ . '/code-quality-check.php';
        $checker = new CodeQualityChecker($this->projectRoot);
        $checker->runAllChecks();
        $output = ob_get_clean();
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        $this->results['code_quality'] = [
            'duration' => $duration,
            'output' => $output,
            'status' => $this->parseTestStatus($output)
        ];
        
        echo "代码质量检查完成，耗时: " . round($duration, 2) . "秒\n";
    }
    
    /**
     * 运行功能测试
     */
    private function runFunctionalTests()
    {
        $startTime = microtime(true);
        
        // 执行功能测试
        ob_start();
        include __DIR__ . '/functional-tests.php';
        $tester = new FunctionalTests($this->baseUrl);
        $tester->runAllTests();
        $output = ob_get_clean();
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        $this->results['functional'] = [
            'duration' => $duration,
            'output' => $output,
            'status' => $this->parseTestStatus($output)
        ];
        
        echo "功能测试完成，耗时: " . round($duration, 2) . "秒\n";
    }
    
    /**
     * 运行性能测试
     */
    private function runPerformanceTests()
    {
        $startTime = microtime(true);
        
        // 执行性能测试
        ob_start();
        include __DIR__ . '/performance-tests.php';
        $tester = new PerformanceTests($this->baseUrl);
        $tester->runAllTests();
        $output = ob_get_clean();
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        $this->results['performance'] = [
            'duration' => $duration,
            'output' => $output,
            'status' => $this->parseTestStatus($output)
        ];
        
        echo "性能测试完成，耗时: " . round($duration, 2) . "秒\n";
    }
    
    /**
     * 解析测试状态
     */
    private function parseTestStatus($output)
    {
        // 从输出中提取通过率
        if (preg_match('/通过率:\s*(\d+(?:\.\d+)?)%/', $output, $matches)) {
            $passRate = floatval($matches[1]);
            return [
                'pass_rate' => $passRate,
                'status' => $passRate >= 80 ? 'PASS' : 'FAIL'
            ];
        }
        
        return [
            'pass_rate' => 0,
            'status' => 'UNKNOWN'
        ];
    }
    
    /**
     * 生成综合报告
     */
    private function generateComprehensiveReport()
    {
        $report = $this->buildReport();
        
        // 输出到控制台
        echo $report['console'];
        
        // 保存到文件
        $this->saveReportToFile($report);
    }
    
    /**
     * 构建报告
     */
    private function buildReport()
    {
        $totalDuration = 0;
        $overallStatus = 'PASS';
        $summary = [];
        
        foreach ($this->results as $testType => $result) {
            $totalDuration += $result['duration'];
            $summary[$testType] = $result['status'];
            
            if ($result['status']['status'] !== 'PASS') {
                $overallStatus = 'FAIL';
            }
        }
        
        $consoleReport = $this->generateConsoleReport($summary, $totalDuration, $overallStatus);
        $htmlReport = $this->generateHtmlReport($summary, $totalDuration, $overallStatus);
        
        return [
            'console' => $consoleReport,
            'html' => $htmlReport,
            'summary' => $summary,
            'overall_status' => $overallStatus,
            'total_duration' => $totalDuration
        ];
    }
    
    /**
     * 生成控制台报告
     */
    private function generateConsoleReport($summary, $totalDuration, $overallStatus)
    {
        $report = "\n" . str_repeat('=', 60) . "\n";
        $report .= "综合测试报告\n";
        $report .= str_repeat('=', 60) . "\n";
        $report .= "测试时间: " . date('Y-m-d H:i:s') . "\n";
        $report .= "总耗时: " . round($totalDuration, 2) . "秒\n";
        $report .= "整体状态: {$overallStatus}\n\n";
        
        $report .= "各阶段测试结果:\n";
        $report .= str_repeat('-', 40) . "\n";
        
        $testNames = [
            'code_quality' => '代码质量检查',
            'functional' => '功能测试',
            'performance' => '性能测试'
        ];
        
        foreach ($summary as $testType => $result) {
            $name = $testNames[$testType] ?? $testType;
            $status = $result['status'] === 'PASS' ? '✓ 通过' : '✗ 失败';
            $passRate = $result['pass_rate'];
            $duration = round($this->results[$testType]['duration'], 2);
            
            $report .= sprintf("%-15s %s (%.1f%%, %.2fs)\n", $name, $status, $passRate, $duration);
        }
        
        $report .= "\n";
        
        // 添加建议
        if ($overallStatus === 'FAIL') {
            $report .= "建议:\n";
            $report .= "- 检查失败的测试项目\n";
            $report .= "- 修复发现的问题后重新运行测试\n";
            $report .= "- 关注性能指标，确保系统稳定性\n";
        } else {
            $report .= "恭喜！所有测试都已通过。\n";
            $report .= "重构工作已成功完成，系统运行正常。\n";
        }
        
        $report .= "\n" . str_repeat('=', 60) . "\n";
        
        return $report;
    }
    
    /**
     * 生成HTML报告
     */
    private function generateHtmlReport($summary, $totalDuration, $overallStatus)
    {
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>类调用方式重构 - 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; }
        .summary-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .summary-table th, .summary-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .summary-table th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>类调用方式重构 - 测试报告</h1>
        <p><strong>测试时间:</strong> ' . date('Y-m-d H:i:s') . '</p>
        <p><strong>总耗时:</strong> ' . round($totalDuration, 2) . '秒</p>
        <p><strong>整体状态:</strong> <span class="status-' . strtolower($overallStatus) . '">' . $overallStatus . '</span></p>
    </div>
    
    <h2>测试结果汇总</h2>
    <table class="summary-table">
        <thead>
            <tr>
                <th>测试阶段</th>
                <th>状态</th>
                <th>通过率</th>
                <th>耗时</th>
            </tr>
        </thead>
        <tbody>';
        
        $testNames = [
            'code_quality' => '代码质量检查',
            'functional' => '功能测试',
            'performance' => '性能测试'
        ];
        
        foreach ($summary as $testType => $result) {
            $name = $testNames[$testType] ?? $testType;
            $statusClass = $result['status'] === 'PASS' ? 'status-pass' : 'status-fail';
            $statusText = $result['status'] === 'PASS' ? '通过' : '失败';
            $passRate = $result['pass_rate'];
            $duration = round($this->results[$testType]['duration'], 2);
            
            $html .= "<tr>
                <td>{$name}</td>
                <td><span class=\"{$statusClass}\">{$statusText}</span></td>
                <td>{$passRate}%</td>
                <td>{$duration}秒</td>
            </tr>";
        }
        
        $html .= '</tbody>
    </table>
    
    <h2>详细输出</h2>';
        
        foreach ($this->results as $testType => $result) {
            $name = $testNames[$testType] ?? $testType;
            $html .= "<div class=\"test-result\">
                <h3>{$name}</h3>
                <pre>" . htmlspecialchars($result['output']) . "</pre>
            </div>";
        }
        
        $html .= '</body></html>';
        
        return $html;
    }
    
    /**
     * 保存报告到文件
     */
    private function saveReportToFile($report)
    {
        $timestamp = date('Y-m-d-H-i-s');
        
        // 保存文本报告
        $textFile = __DIR__ . "/comprehensive-report-{$timestamp}.txt";
        file_put_contents($textFile, $report['console']);
        
        // 保存HTML报告
        $htmlFile = __DIR__ . "/comprehensive-report-{$timestamp}.html";
        file_put_contents($htmlFile, $report['html']);
        
        // 保存JSON数据
        $jsonFile = __DIR__ . "/comprehensive-report-{$timestamp}.json";
        file_put_contents($jsonFile, json_encode([
            'timestamp' => date('Y-m-d H:i:s'),
            'base_url' => $this->baseUrl,
            'project_root' => $this->projectRoot,
            'summary' => $report['summary'],
            'overall_status' => $report['overall_status'],
            'total_duration' => $report['total_duration'],
            'results' => $this->results
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "报告已保存:\n";
        echo "- 文本报告: {$textFile}\n";
        echo "- HTML报告: {$htmlFile}\n";
        echo "- JSON数据: {$jsonFile}\n";
    }
}

// 运行综合测试
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://news.test.jijiaox.com';
    $projectRoot = $argv[2] ?? null;
    
    $runner = new TestRunner($baseUrl, $projectRoot);
    $runner->runAllTests();
} else {
    echo "请在命令行中运行此脚本\n";
    echo "用法: php run-all-tests.php [base_url] [project_root]\n";
}
