<?php
/**
 * 功能测试脚本
 * 用于验证重构后的代码功能是否正常
 */

require_once __DIR__ . '/../../vendor/autoload.php';

class FunctionalTests
{
    private $baseUrl;
    private $testResults = [];
    
    public function __construct($baseUrl = 'http://news.test.jijiaox.com')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始执行功能测试...\n\n";
        
        // 前台页面测试
        $this->testHomePage();
        $this->testArticleDetail();
        $this->testCategoryPage();
        $this->testSearchPage();
        
        // API接口测试
        $this->testArticleAPI();
        $this->testUserAPI();
        
        // 输出测试结果
        $this->outputResults();
    }
    
    /**
     * 测试首页功能
     */
    private function testHomePage()
    {
        echo "测试用例1：首页功能测试\n";
        
        $url = $this->baseUrl . '/';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '首页无法访问';
        } elseif (strpos($response, '<!DOCTYPE html') === false) {
            $success = false;
            $message = '首页返回内容不是有效的HTML';
        } elseif (strpos($response, 'error') !== false || strpos($response, 'Error') !== false) {
            $success = false;
            $message = '首页包含错误信息';
        }
        
        $this->recordResult('首页功能测试', $success, $message);
    }
    
    /**
     * 测试文章详情页
     */
    private function testArticleDetail()
    {
        echo "测试用例2：文章详情页测试\n";
        
        // 假设文章ID为1，实际测试时需要使用真实的文章ID
        $url = $this->baseUrl . '/article/1';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '文章详情页无法访问';
        } elseif (strpos($response, '文章不存在') !== false) {
            $success = false;
            $message = '测试文章不存在，请检查测试数据';
        } elseif (strpos($response, 'error') !== false || strpos($response, 'Error') !== false) {
            $success = false;
            $message = '文章详情页包含错误信息';
        }
        
        $this->recordResult('文章详情页测试', $success, $message);
    }
    
    /**
     * 测试分类页面
     */
    private function testCategoryPage()
    {
        echo "测试用例3：分类页面测试\n";
        
        // 假设分类ID为1，实际测试时需要使用真实的分类ID
        $url = $this->baseUrl . '/category/1';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '分类页面无法访问';
        } elseif (strpos($response, '分类不存在') !== false) {
            $success = false;
            $message = '测试分类不存在，请检查测试数据';
        } elseif (strpos($response, 'error') !== false || strpos($response, 'Error') !== false) {
            $success = false;
            $message = '分类页面包含错误信息';
        }
        
        $this->recordResult('分类页面测试', $success, $message);
    }
    
    /**
     * 测试搜索页面
     */
    private function testSearchPage()
    {
        echo "测试用例4：搜索功能测试\n";
        
        $url = $this->baseUrl . '/search?keyword=测试';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '搜索页面无法访问';
        } elseif (strpos($response, 'error') !== false || strpos($response, 'Error') !== false) {
            $success = false;
            $message = '搜索页面包含错误信息';
        }
        
        $this->recordResult('搜索功能测试', $success, $message);
    }
    
    /**
     * 测试文章API
     */
    private function testArticleAPI()
    {
        echo "测试用例5：文章管理API测试\n";
        
        // 测试获取文章列表
        $url = $this->baseUrl . '/api/articles';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '文章API无法访问';
        } else {
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $success = false;
                $message = '文章API返回的不是有效的JSON';
            } elseif (!isset($data['code'])) {
                $success = false;
                $message = '文章API返回格式不正确';
            }
        }
        
        $this->recordResult('文章管理API测试', $success, $message);
    }
    
    /**
     * 测试用户API
     */
    private function testUserAPI()
    {
        echo "测试用例6：用户管理API测试\n";
        
        // 测试获取用户列表（需要认证）
        $url = $this->baseUrl . '/api/users';
        $response = $this->makeRequest($url);
        
        $success = true;
        $message = '';
        
        if ($response === false) {
            $success = false;
            $message = '用户API无法访问';
        } else {
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $success = false;
                $message = '用户API返回的不是有效的JSON';
            } elseif (!isset($data['code'])) {
                $success = false;
                $message = '用户API返回格式不正确';
            } elseif ($data['code'] == 401) {
                // 401是预期的，因为没有认证
                $message = '认证功能正常（返回401）';
            }
        }
        
        $this->recordResult('用户管理API测试', $success, $message);
    }
    
    /**
     * 发起HTTP请求
     */
    private function makeRequest($url, $method = 'GET', $data = null)
    {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Refactor Test Script');
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        curl_close($ch);
        
        if ($response === false || $httpCode >= 500) {
            return false;
        }
        
        return $response;
    }
    
    /**
     * 记录测试结果
     */
    private function recordResult($testName, $success, $message = '')
    {
        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'message' => $message,
            'time' => date('Y-m-d H:i:s')
        ];
        
        $status = $success ? '✓ 通过' : '✗ 失败';
        echo "  {$status}: {$testName}";
        if ($message) {
            echo " - {$message}";
        }
        echo "\n";
    }
    
    /**
     * 输出测试结果
     */
    private function outputResults()
    {
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "测试结果汇总\n";
        echo str_repeat('=', 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['success']) {
                $passedTests++;
            }
        }
        
        $failedTests = $totalTests - $passedTests;
        
        echo "总测试数: {$totalTests}\n";
        echo "通过: {$passedTests}\n";
        echo "失败: {$failedTests}\n";
        echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";
        
        if ($failedTests > 0) {
            echo "失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "- {$result['name']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存测试结果到文件
        $this->saveResultsToFile();
    }
    
    /**
     * 保存测试结果到文件
     */
    private function saveResultsToFile()
    {
        $filename = __DIR__ . '/test-results-' . date('Y-m-d-H-i-s') . '.json';
        file_put_contents($filename, json_encode($this->testResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n测试结果已保存到: {$filename}\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://news.test.jijiaox.com';
    $tester = new FunctionalTests($baseUrl);
    $tester->runAllTests();
} else {
    echo "请在命令行中运行此脚本\n";
    echo "用法: php functional-tests.php [base_url]\n";
}
