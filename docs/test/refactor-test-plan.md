# 类调用方式重构测试计划

## 1. 测试目标

验证所有修改后的代码能正常工作，确保：
1. 功能完整性：所有原有功能正常工作
2. 性能稳定性：修改后性能不会明显下降
3. 代码质量：新的调用方式符合要求

## 2. 测试范围

### 2.1 控制器测试

#### 2.1.1 前台控制器 (app/index/controller/Index.php)

**测试用例1：首页功能测试**
- 测试URL: `/`
- 预期结果: 页面正常显示，包含分类列表、轮播图、热门新闻、最新文章
- 验证点: 
  - 分类数据正常加载
  - 文章数据正常显示
  - 页面渲染无错误

**测试用例2：文章详情页测试**
- 测试URL: `/article/{id}`
- 预期结果: 文章详情正常显示，阅读量正常增加
- 验证点:
  - 文章内容正确显示
  - 相关文章推荐正常
  - 阅读量计数功能正常

**测试用例3：分类页面测试**
- 测试URL: `/category/{id}`
- 预期结果: 分类下的文章列表正常显示
- 验证点:
  - 分类信息正确
  - 文章列表正常分页
  - 面包屑导航正确

**测试用例4：搜索功能测试**
- 测试URL: `/search?keyword=测试`
- 预期结果: 搜索结果正常显示
- 验证点:
  - 搜索结果准确
  - 分页功能正常
  - 关键词高亮显示

#### 2.1.2 API控制器测试

**测试用例5：文章管理API测试**
- 测试接口: `/api/articles`
- 测试方法: GET, POST, PUT, DELETE
- 预期结果: 所有CRUD操作正常
- 验证点:
  - 文章列表获取正常
  - 文章创建功能正常
  - 文章更新功能正常
  - 文章删除功能正常

**测试用例6：用户管理API测试**
- 测试接口: `/api/users`
- 测试方法: GET, POST, PUT, DELETE
- 预期结果: 所有用户管理操作正常
- 验证点:
  - 用户列表获取正常
  - 用户创建功能正常
  - 用户更新功能正常
  - 用户删除功能正常

### 2.2 服务类测试

#### 2.2.1 ArticleService 测试

**测试用例7：文章服务功能测试**
- 测试方法: `getById()`, `create()`, `update()`, `delete()`
- 预期结果: 所有方法正常工作
- 验证点:
  - 数据获取正确
  - 数据创建成功
  - 数据更新成功
  - 数据删除成功

#### 2.2.2 AdminUserService 测试

**测试用例8：用户服务功能测试**
- 测试方法: `getById()`, `create()`, `update()`, `delete()`, `changePassword()`
- 预期结果: 所有方法正常工作
- 验证点:
  - 用户数据操作正常
  - 密码修改功能正常
  - 数据验证正常

#### 2.2.3 ArticleCategoryService 测试

**测试用例9：分类服务功能测试**
- 测试方法: `getMenuTree()`, `create()`, `update()`, `delete()`
- 预期结果: 所有方法正常工作
- 验证点:
  - 分类树结构正确
  - 分类CRUD操作正常
  - 缓存功能正常

### 2.3 中间件测试

**测试用例10：认证中间件测试**
- 测试场景: 访问需要认证的API接口
- 预期结果: 认证功能正常
- 验证点:
  - 未登录用户被正确拦截
  - 已登录用户正常通过
  - 权限验证正常

## 3. 性能测试

### 3.1 响应时间测试

**测试用例11：页面响应时间测试**
- 测试页面: 首页、文章详情页、分类页面
- 测试方法: 使用浏览器开发者工具或性能测试工具
- 预期结果: 响应时间与修改前相当或更好
- 验证标准: 页面加载时间 < 2秒

### 3.2 并发测试

**测试用例12：并发访问测试**
- 测试场景: 模拟多用户同时访问
- 测试工具: Apache Bench (ab) 或类似工具
- 预期结果: 系统稳定运行，无明显性能下降
- 验证标准: 并发100用户时响应时间 < 5秒

## 4. 代码质量测试

### 4.1 静态代码分析

**测试用例13：代码规范检查**
- 检查内容: 
  - 确认所有 $this->xxx 调用已被正确替换
  - 确认性能优化措施已实施
  - 确认代码符合PSR规范
- 验证方法: 代码审查和静态分析工具

### 4.2 内存使用测试

**测试用例14：内存使用情况测试**
- 测试方法: 使用 memory_get_usage() 监控内存使用
- 预期结果: 内存使用量合理，无明显内存泄漏
- 验证标准: 内存使用量与修改前相当

## 5. 错误处理测试

### 5.1 异常处理测试

**测试用例15：异常情况处理测试**
- 测试场景: 
  - 访问不存在的文章
  - 提交无效数据
  - 数据库连接异常
- 预期结果: 错误信息正确显示，系统稳定运行
- 验证点: 错误页面正常显示，日志记录正确

## 6. 缓存功能测试

### 6.1 缓存有效性测试

**测试用例16：缓存功能测试**
- 测试内容: 分类树缓存、配置缓存等
- 测试方法: 
  1. 首次访问，验证缓存生成
  2. 再次访问，验证缓存命中
  3. 数据更新后，验证缓存清除
- 预期结果: 缓存功能正常工作

## 7. 测试执行步骤

### 7.1 准备工作
1. 备份当前数据库
2. 准备测试数据
3. 配置测试环境

### 7.2 执行顺序
1. 功能测试 (测试用例1-10)
2. 性能测试 (测试用例11-12)
3. 代码质量测试 (测试用例13-14)
4. 错误处理测试 (测试用例15)
5. 缓存功能测试 (测试用例16)

### 7.3 测试记录
- 记录每个测试用例的执行结果
- 记录发现的问题和解决方案
- 记录性能数据对比

## 8. 验收标准

### 8.1 功能验收标准
- 所有原有功能正常工作
- 新的调用方式符合要求
- 无功能回归问题

### 8.2 性能验收标准
- 页面响应时间不超过修改前的120%
- 内存使用量不超过修改前的110%
- 并发处理能力不低于修改前

### 8.3 代码质量验收标准
- 所有 $this->xxx 调用已正确替换
- 性能优化措施已实施
- 代码符合团队规范

## 9. 风险评估

### 9.1 潜在风险
- 频繁实例化可能导致性能下降
- 代码修改可能引入新的bug
- 缓存机制可能受到影响

### 9.2 风险缓解措施
- 充分的性能测试
- 全面的功能测试
- 渐进式部署策略
