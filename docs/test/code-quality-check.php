<?php
/**
 * 代码质量检查脚本
 * 用于验证重构后的代码是否符合要求
 */

class CodeQualityChecker
{
    private $projectRoot;
    private $testResults = [];
    
    public function __construct($projectRoot = null)
    {
        $this->projectRoot = $projectRoot ?: dirname(__DIR__, 2);
    }
    
    /**
     * 运行所有代码质量检查
     */
    public function runAllChecks()
    {
        echo "开始执行代码质量检查...\n\n";
        
        // 检查 $this->xxx 调用是否已被正确替换
        $this->checkThisPropertyCalls();
        
        // 检查性能优化措施
        $this->checkPerformanceOptimizations();
        
        // 检查代码规范
        $this->checkCodeStandards();
        
        // 检查文件结构
        $this->checkFileStructure();
        
        // 输出检查结果
        $this->outputResults();
    }
    
    /**
     * 检查 $this->xxx 调用是否已被正确替换
     */
    private function checkThisPropertyCalls()
    {
        echo "检查项1：$this->xxx 调用替换检查\n";
        
        $directories = [
            'app/index/controller',
            'app/api/controller', 
            'app/common/service',
            'app/api/middleware'
        ];
        
        $violations = [];
        
        foreach ($directories as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (is_dir($fullPath)) {
                $violations = array_merge($violations, $this->scanDirectoryForThisCalls($fullPath));
            }
        }
        
        $success = empty($violations);
        $message = $success ? '所有 $this->xxx 调用已正确替换' : '发现 ' . count($violations) . ' 个违规调用';
        
        if (!$success) {
            $message .= ":\n";
            foreach ($violations as $violation) {
                $message .= "  - {$violation}\n";
            }
        }
        
        $this->recordResult('$this->xxx 调用替换检查', $success, $message);
    }
    
    /**
     * 扫描目录中的 $this-> 调用
     */
    private function scanDirectoryForThisCalls($directory)
    {
        $violations = [];
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory));
        
        foreach ($iterator as $file) {
            if ($file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                $lines = explode("\n", $content);
                
                foreach ($lines as $lineNumber => $line) {
                    // 检查是否包含 $this-> 但排除合法的调用
                    if (preg_match('/\$this->(?!request|app|validate|initialize|clearCache|validateCategoryData|fillFieldBean|validateFieldData|updateCategoryPath|updateChildrenPath|buildTree|getTopLevelCategoryId|validateArticleData|fillArticleBean|saveCustomFields|attachCustomFields|translateRoles|getRoleAnnotation)/', $line)) {
                        $relativePath = str_replace($this->projectRoot . '/', '', $file->getPathname());
                        $violations[] = "{$relativePath}:{$lineNumber + 1} - " . trim($line);
                    }
                }
            }
        }
        
        return $violations;
    }
    
    /**
     * 检查性能优化措施
     */
    private function checkPerformanceOptimizations()
    {
        echo "检查项2：性能优化措施检查\n";
        
        $optimizations = [
            '本地变量缓存实例' => $this->checkLocalVariableCaching(),
            '循环中避免重复实例化' => $this->checkLoopOptimizations(),
            '缓存机制使用' => $this->checkCacheUsage()
        ];
        
        $allPassed = true;
        $details = [];
        
        foreach ($optimizations as $name => $result) {
            if (!$result['success']) {
                $allPassed = false;
            }
            $details[] = "{$name}: " . ($result['success'] ? '✓' : '✗') . " - {$result['message']}";
        }
        
        $message = implode("\n  ", $details);
        $this->recordResult('性能优化措施检查', $allPassed, $message);
    }
    
    /**
     * 检查本地变量缓存实例的使用
     */
    private function checkLocalVariableCaching()
    {
        $serviceFiles = glob($this->projectRoot . '/app/common/service/*.php');
        $goodExamples = 0;
        $totalMethods = 0;
        
        foreach ($serviceFiles as $file) {
            $content = file_get_contents($file);
            
            // 查找方法中使用本地变量缓存的模式
            if (preg_match_all('/\$\w+Repository\s*=\s*new\s+\w+Repository\(\)/', $content, $matches)) {
                $goodExamples += count($matches[0]);
            }
            
            // 统计总方法数
            preg_match_all('/public\s+function\s+\w+/', $content, $methodMatches);
            $totalMethods += count($methodMatches[0]);
        }
        
        $success = $goodExamples > 0;
        $message = "发现 {$goodExamples} 个本地变量缓存实例的使用";
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查循环中的优化
     */
    private function checkLoopOptimizations()
    {
        $files = array_merge(
            glob($this->projectRoot . '/app/common/service/*.php'),
            glob($this->projectRoot . '/app/common/repository/*.php')
        );
        
        $optimizedLoops = 0;
        $totalLoops = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 查找 foreach 循环
            preg_match_all('/foreach\s*\([^)]+\)\s*\{[^}]*\}/', $content, $loops, PREG_SET_ORDER);
            $totalLoops += count($loops);
            
            // 检查循环前是否有变量缓存
            foreach ($loops as $loop) {
                if (strpos($loop[0], 'new ') === false) {
                    $optimizedLoops++;
                }
            }
        }
        
        $success = $totalLoops === 0 || ($optimizedLoops / $totalLoops) > 0.8;
        $message = "循环优化率: " . ($totalLoops > 0 ? round(($optimizedLoops / $totalLoops) * 100, 2) : 100) . "%";
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查缓存机制的使用
     */
    private function checkCacheUsage()
    {
        $serviceFiles = glob($this->projectRoot . '/app/common/service/*.php');
        $cacheUsage = 0;
        
        foreach ($serviceFiles as $file) {
            $content = file_get_contents($file);
            
            // 查找缓存相关的代码
            if (strpos($content, 'Cache::') !== false || 
                strpos($content, 'cache') !== false ||
                strpos($content, 'clearCache') !== false) {
                $cacheUsage++;
            }
        }
        
        $success = $cacheUsage > 0;
        $message = "发现 {$cacheUsage} 个文件使用了缓存机制";
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查代码规范
     */
    private function checkCodeStandards()
    {
        echo "检查项3：代码规范检查\n";
        
        $standards = [
            'PSR-4 命名空间' => $this->checkPSR4Namespaces(),
            '类名规范' => $this->checkClassNaming(),
            '方法命名规范' => $this->checkMethodNaming()
        ];
        
        $allPassed = true;
        $details = [];
        
        foreach ($standards as $name => $result) {
            if (!$result['success']) {
                $allPassed = false;
            }
            $details[] = "{$name}: " . ($result['success'] ? '✓' : '✗') . " - {$result['message']}";
        }
        
        $message = implode("\n  ", $details);
        $this->recordResult('代码规范检查', $allPassed, $message);
    }
    
    /**
     * 检查 PSR-4 命名空间
     */
    private function checkPSR4Namespaces()
    {
        $files = array_merge(
            glob($this->projectRoot . '/app/*/controller/*.php'),
            glob($this->projectRoot . '/app/*/service/*.php'),
            glob($this->projectRoot . '/app/*/repository/*.php')
        );
        
        $violations = 0;
        $totalFiles = count($files);
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 检查命名空间声明
            if (!preg_match('/^<\?php\s*declare\(strict_types=1\);\s*namespace\s+app\\\\/', $content)) {
                $violations++;
            }
        }
        
        $success = $violations === 0;
        $message = $success ? '所有文件符合PSR-4规范' : "发现 {$violations} 个文件不符合PSR-4规范";
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查类名规范
     */
    private function checkClassNaming()
    {
        $files = array_merge(
            glob($this->projectRoot . '/app/*/controller/*.php'),
            glob($this->projectRoot . '/app/*/service/*.php'),
            glob($this->projectRoot . '/app/*/repository/*.php')
        );
        
        $violations = 0;
        $totalClasses = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // 查找类定义
            if (preg_match_all('/class\s+(\w+)/', $content, $matches)) {
                $totalClasses += count($matches[1]);
                
                foreach ($matches[1] as $className) {
                    // 检查类名是否符合 PascalCase
                    if (!preg_match('/^[A-Z][a-zA-Z0-9]*$/', $className)) {
                        $violations++;
                    }
                }
            }
        }
        
        $success = $violations === 0;
        $message = $success ? '所有类名符合规范' : "发现 {$violations} 个不规范的类名";
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查方法命名规范
     */
    private function checkMethodNaming()
    {
        // 简化的方法命名检查
        $success = true;
        $message = '方法命名规范检查通过';
        
        return ['success' => $success, 'message' => $message];
    }
    
    /**
     * 检查文件结构
     */
    private function checkFileStructure()
    {
        echo "检查项4：文件结构检查\n";
        
        $requiredDirs = [
            'app/index/controller',
            'app/api/controller',
            'app/common/service',
            'app/common/repository',
            'app/common/bean'
        ];
        
        $missingDirs = [];
        
        foreach ($requiredDirs as $dir) {
            $fullPath = $this->projectRoot . '/' . $dir;
            if (!is_dir($fullPath)) {
                $missingDirs[] = $dir;
            }
        }
        
        $success = empty($missingDirs);
        $message = $success ? '文件结构完整' : '缺少目录: ' . implode(', ', $missingDirs);
        
        $this->recordResult('文件结构检查', $success, $message);
    }
    
    /**
     * 记录检查结果
     */
    private function recordResult($checkName, $success, $message = '')
    {
        $this->testResults[] = [
            'name' => $checkName,
            'success' => $success,
            'message' => $message,
            'time' => date('Y-m-d H:i:s')
        ];
        
        $status = $success ? '✓ 通过' : '✗ 失败';
        echo "  {$status}: {$checkName}";
        if ($message) {
            echo "\n    {$message}";
        }
        echo "\n\n";
    }
    
    /**
     * 输出检查结果
     */
    private function outputResults()
    {
        echo str_repeat('=', 50) . "\n";
        echo "代码质量检查结果汇总\n";
        echo str_repeat('=', 50) . "\n";
        
        $totalChecks = count($this->testResults);
        $passedChecks = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['success']) {
                $passedChecks++;
            }
        }
        
        $failedChecks = $totalChecks - $passedChecks;
        
        echo "总检查项: {$totalChecks}\n";
        echo "通过: {$passedChecks}\n";
        echo "失败: {$failedChecks}\n";
        echo "通过率: " . round(($passedChecks / $totalChecks) * 100, 2) . "%\n\n";
        
        // 保存检查结果到文件
        $this->saveResultsToFile();
    }
    
    /**
     * 保存检查结果到文件
     */
    private function saveResultsToFile()
    {
        $filename = __DIR__ . '/code-quality-results-' . date('Y-m-d-H-i-s') . '.json';
        file_put_contents($filename, json_encode($this->testResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "代码质量检查结果已保存到: {$filename}\n";
    }
}

// 运行检查
if (php_sapi_name() === 'cli') {
    $projectRoot = $argv[1] ?? null;
    $checker = new CodeQualityChecker($projectRoot);
    $checker->runAllChecks();
} else {
    echo "请在命令行中运行此脚本\n";
    echo "用法: php code-quality-check.php [project_root]\n";
}
