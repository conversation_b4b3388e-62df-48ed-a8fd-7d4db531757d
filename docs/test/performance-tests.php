<?php
/**
 * 性能测试脚本
 * 用于验证重构后的代码性能是否符合要求
 */

require_once __DIR__ . '/../../vendor/autoload.php';

class PerformanceTests
{
    private $baseUrl;
    private $testResults = [];
    
    public function __construct($baseUrl = 'http://news.test.jijiaox.com')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * 运行所有性能测试
     */
    public function runAllTests()
    {
        echo "开始执行性能测试...\n\n";
        
        // 响应时间测试
        $this->testPageResponseTime();
        $this->testAPIResponseTime();
        
        // 内存使用测试
        $this->testMemoryUsage();
        
        // 并发测试
        $this->testConcurrency();
        
        // 输出测试结果
        $this->outputResults();
    }
    
    /**
     * 测试页面响应时间
     */
    private function testPageResponseTime()
    {
        echo "测试用例11：页面响应时间测试\n";
        
        $pages = [
            '首页' => '/',
            '文章详情页' => '/article/1',
            '分类页面' => '/category/1',
            '搜索页面' => '/search?keyword=测试'
        ];
        
        foreach ($pages as $pageName => $path) {
            $url = $this->baseUrl . $path;
            $times = [];
            
            // 进行5次测试取平均值
            for ($i = 0; $i < 5; $i++) {
                $startTime = microtime(true);
                $response = $this->makeRequest($url);
                $endTime = microtime(true);
                
                if ($response !== false) {
                    $times[] = ($endTime - $startTime) * 1000; // 转换为毫秒
                }
                
                // 避免请求过于频繁
                usleep(100000); // 0.1秒
            }
            
            if (!empty($times)) {
                $avgTime = array_sum($times) / count($times);
                $success = $avgTime < 2000; // 2秒内为合格
                $message = sprintf('平均响应时间: %.2fms', $avgTime);
                
                $this->recordResult("{$pageName}响应时间", $success, $message);
            } else {
                $this->recordResult("{$pageName}响应时间", false, '无法获取响应时间');
            }
        }
    }
    
    /**
     * 测试API响应时间
     */
    private function testAPIResponseTime()
    {
        echo "测试用例12：API响应时间测试\n";
        
        $apis = [
            '文章列表API' => '/api/articles',
            '分类列表API' => '/api/categories',
            '用户列表API' => '/api/users'
        ];
        
        foreach ($apis as $apiName => $path) {
            $url = $this->baseUrl . $path;
            $times = [];
            
            // 进行5次测试取平均值
            for ($i = 0; $i < 5; $i++) {
                $startTime = microtime(true);
                $response = $this->makeRequest($url);
                $endTime = microtime(true);
                
                if ($response !== false) {
                    $times[] = ($endTime - $startTime) * 1000; // 转换为毫秒
                }
                
                // 避免请求过于频繁
                usleep(100000); // 0.1秒
            }
            
            if (!empty($times)) {
                $avgTime = array_sum($times) / count($times);
                $success = $avgTime < 1000; // API 1秒内为合格
                $message = sprintf('平均响应时间: %.2fms', $avgTime);
                
                $this->recordResult("{$apiName}响应时间", $success, $message);
            } else {
                $this->recordResult("{$apiName}响应时间", false, '无法获取响应时间');
            }
        }
    }
    
    /**
     * 测试内存使用情况
     */
    private function testMemoryUsage()
    {
        echo "测试用例13：内存使用情况测试\n";
        
        // 这个测试需要在实际的PHP环境中运行，这里只是示例
        $initialMemory = memory_get_usage();
        
        // 模拟一些操作
        $testData = [];
        for ($i = 0; $i < 1000; $i++) {
            $testData[] = str_repeat('test', 100);
        }
        
        $peakMemory = memory_get_peak_usage();
        $currentMemory = memory_get_usage();
        
        // 清理测试数据
        unset($testData);
        
        $memoryUsed = $peakMemory - $initialMemory;
        $memoryUsedMB = $memoryUsed / 1024 / 1024;
        
        $success = $memoryUsedMB < 50; // 50MB内为合格
        $message = sprintf('峰值内存使用: %.2fMB', $memoryUsedMB);
        
        $this->recordResult('内存使用情况', $success, $message);
    }
    
    /**
     * 测试并发处理能力
     */
    private function testConcurrency()
    {
        echo "测试用例14：并发处理能力测试\n";
        
        $url = $this->baseUrl . '/';
        $concurrentRequests = 10; // 并发请求数
        $totalRequests = 50; // 总请求数
        
        $startTime = microtime(true);
        $successCount = 0;
        $failCount = 0;
        
        // 模拟并发请求（简化版本）
        for ($i = 0; $i < $totalRequests; $i += $concurrentRequests) {
            $batch = min($concurrentRequests, $totalRequests - $i);
            
            for ($j = 0; $j < $batch; $j++) {
                $response = $this->makeRequest($url);
                if ($response !== false) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }
            
            // 短暂休息
            usleep(10000); // 0.01秒
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        $requestsPerSecond = $totalRequests / $totalTime;
        
        $success = $requestsPerSecond > 10 && ($failCount / $totalRequests) < 0.05; // 每秒10个请求且失败率小于5%
        $message = sprintf('处理能力: %.2f请求/秒, 成功率: %.2f%%', 
            $requestsPerSecond, 
            ($successCount / $totalRequests) * 100
        );
        
        $this->recordResult('并发处理能力', $success, $message);
    }
    
    /**
     * 发起HTTP请求
     */
    private function makeRequest($url, $timeout = 10)
    {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Performance Test Script');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        curl_close($ch);
        
        if ($response === false || $httpCode >= 500) {
            return false;
        }
        
        return $response;
    }
    
    /**
     * 记录测试结果
     */
    private function recordResult($testName, $success, $message = '')
    {
        $this->testResults[] = [
            'name' => $testName,
            'success' => $success,
            'message' => $message,
            'time' => date('Y-m-d H:i:s')
        ];
        
        $status = $success ? '✓ 通过' : '✗ 失败';
        echo "  {$status}: {$testName}";
        if ($message) {
            echo " - {$message}";
        }
        echo "\n";
    }
    
    /**
     * 输出测试结果
     */
    private function outputResults()
    {
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "性能测试结果汇总\n";
        echo str_repeat('=', 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['success']) {
                $passedTests++;
            }
        }
        
        $failedTests = $totalTests - $passedTests;
        
        echo "总测试数: {$totalTests}\n";
        echo "通过: {$passedTests}\n";
        echo "失败: {$failedTests}\n";
        echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n\n";
        
        if ($failedTests > 0) {
            echo "未通过的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "- {$result['name']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存测试结果到文件
        $this->saveResultsToFile();
    }
    
    /**
     * 保存测试结果到文件
     */
    private function saveResultsToFile()
    {
        $filename = __DIR__ . '/performance-results-' . date('Y-m-d-H-i-s') . '.json';
        file_put_contents($filename, json_encode($this->testResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n性能测试结果已保存到: {$filename}\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://news.test.jijiaox.com';
    $tester = new PerformanceTests($baseUrl);
    $tester->runAllTests();
} else {
    echo "请在命令行中运行此脚本\n";
    echo "用法: php performance-tests.php [base_url]\n";
}
