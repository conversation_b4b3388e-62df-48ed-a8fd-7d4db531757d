---
type: "always_apply"
description: "基本要求"
---
【项目说明】
这是一个多模块的ThinkPHP8.0项目，最好不要改动最外层的配置文件
当前项目域名为 news.test.jijiaox.com，优先使用http协议
【项目需求】
只需要超级管理员+内容管理员两种角色
我要做一个自定义门户样式的新闻网站，前后端不分离，SEO友好
【开发要求】
必须遵守：路由使用分组模式
所有查询使用bean+repo的方式查询数据（基于extend/orm）
数据库表、字段使用小写下划线，配置文件在.env文件中
Bean小驼峰，实体、new等均使用大驼峰
路由分组，接口必须使用Restful风格，路径简明扼要
把单模块使用的函数写到模块下的common.php，可以全部使用的放到app/common.php
所有的repo中save和update合并为save方法，同时永远不要在repo中做业务逻辑，只做数据的CRUD操作