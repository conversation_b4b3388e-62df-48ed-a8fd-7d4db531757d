<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\bean\PortalBannerBean;
use app\common\repository\PortalBannerRepository;
use app\common\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;
use Exception;

/**
 * 轮播Banner API控制器
 */
class PortalBannerController
{
    private PortalBannerRepository $bannerRepository;
    
    public function __construct()
    {
        $this->bannerRepository = new PortalBannerRepository();
    }
    
    /**
     * 获取Banner列表
     */
    #[RequireRole(message: '获取Banner列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 20);
            $title = $request->param('title', '');
            $isEnabled = $request->param('is_enabled');
            
            $conditions = [];
            if (!empty($title)) {
                $conditions['title'] = $title;
            }
            if ($isEnabled !== null) {
                $conditions['is_enabled'] = (int)$isEnabled;
            }
            
            $result = $this->bannerRepository->findWithPagination($conditions, $page, $limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取Banner列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取启用的Banner列表（前台使用）
     */
    public function enabled(Request $request): Response
    {
        try {
            $limit = (int)$request->param('limit', 0);
            $banners = $this->bannerRepository->findDisplayable($limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => array_map(function($banner) {
                    return $banner->toArray();
                }, $banners)
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取Banner列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取Banner详情
     */
    #[RequireRole(message: '获取Banner详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('Banner ID不能为空');
            }
            
            $banner = $this->bannerRepository->findById($id);
            if (!$banner) {
                throw new ValidateException('Banner不存在');
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $banner->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取Banner详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建Banner
     */
    #[RequireRole(message: '创建Banner需要管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'title', 'image_url', 'link_url', 'description',
                'sort_order', 'is_enabled', 'target_type',
                'start_time', 'end_time'
            ]);
            
            $banner = new PortalBannerBean();
            $banner->title = $data['title'] ?? '';
            $banner->imageUrl = $data['image_url'] ?? '';
            $banner->linkUrl = $data['link_url'] ?? '';
            $banner->description = $data['description'] ?? null;
            $banner->sortOrder = (int)($data['sort_order'] ?? 0);
            $banner->isEnabled = (int)($data['is_enabled'] ?? 1);
            $banner->targetType = $data['target_type'] ?? '_self';
            $banner->startTime = $data['start_time'] ?? null;
            $banner->endTime = $data['end_time'] ?? null;
            
            $result = $this->bannerRepository->create($banner);
            
            // 清除相关缓存
            cache('portal_banners', null);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建Banner失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新Banner
     */
    #[RequireRole(message: '更新Banner需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('Banner ID不能为空');
            }
            
            $banner = $this->bannerRepository->findById($id);
            if (!$banner) {
                throw new ValidateException('Banner不存在');
            }
            
            $data = $request->only([
                'title', 'image_url', 'link_url', 'description',
                'sort_order', 'is_enabled', 'target_type',
                'start_time', 'end_time'
            ]);
            
            $banner->title = $data['title'] ?? $banner->title;
            $banner->imageUrl = $data['image_url'] ?? $banner->imageUrl;
            $banner->linkUrl = $data['link_url'] ?? $banner->linkUrl;
            $banner->description = $data['description'] ?? $banner->description;
            $banner->sortOrder = (int)($data['sort_order'] ?? $banner->sortOrder);
            $banner->isEnabled = (int)($data['is_enabled'] ?? $banner->isEnabled);
            $banner->targetType = $data['target_type'] ?? $banner->targetType;
            $banner->startTime = $data['start_time'] ?? $banner->startTime;
            $banner->endTime = $data['end_time'] ?? $banner->endTime;
            
            $result = $this->bannerRepository->update($banner);
            
            // 清除相关缓存
            cache('portal_banners', null);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新Banner失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除Banner
     */
    #[RequireRole(message: '删除Banner需要管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('Banner ID不能为空');
            }
            
            $banner = $this->bannerRepository->findById($id);
            if (!$banner) {
                throw new ValidateException('Banner不存在');
            }
            
            $result = $this->bannerRepository->delete($id);
            
            if ($result) {
                // 清除相关缓存
                cache('portal_banners', null);
                
                return json([
                    'code' => 200,
                    'message' => '删除成功',
                    'data' => null
                ]);
            } else {
                throw new Exception('删除失败');
            }
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除Banner失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * Banner点击统计
     */
    public function click(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('Banner ID不能为空');
            }
            
            $this->bannerRepository->updateClickCount($id);
            
            return json([
                'code' => 200,
                'message' => '统计成功',
                'data' => null
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
