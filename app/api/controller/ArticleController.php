<?php
declare(strict_types=1);

namespace app\api\controller;

use app\api\annotation\RequireRole;
use app\common\service\ArticleService;
use app\common\repository\ArticleRepository;
use think\Exception;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 文章管理控制器
 */
class ArticleController
{
    public function __construct()
    {
    }

    /**
     * 获取文章列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看文章列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 15);
            $includeCustomFields = (bool)$request->param('include_custom_fields', false);
            
            // 构建查询条件
            $where = [];
            if ($request->has('title')) {
                $where['title'] = $request->param('title');
            }
            if ($request->has('author')) {
                $where['author'] = $request->param('author');
            }
            if ($request->has('status')) {
                $where['status'] = $request->param('status');
            }
            if ($request->has('category_id')) {
                $where['category_id'] = $request->param('category_id');
            }
            if ($request->has('is_top')) {
                $where['is_top'] = $request->param('is_top');
            }
            
            $result = (new ArticleService())->getList($where, $page, $limit, $includeCustomFields);
            
            // 转换Bean为数组
            $list = [];
            foreach ($result['list'] as $article) {
                $articleArray = $article->toArray();
                if (isset($article->customFields)) {
                    $articleArray['custom_fields'] = $article->customFields;
                }
                $list[] = $articleArray;
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $list,
                    'total' => $result['total'],
                    'page' => $result['page'],
                    'limit' => $result['limit'],
                    'pages' => $result['pages']
                ]
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取文章列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取文章详情
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看文章详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('文章ID不能为空');
            }
            
            $includeCustomFields = (bool)$request->param('include_custom_fields', true);
            $article = (new ArticleService())->getById($id, $includeCustomFields);
            
            if (!$article) {
                return json([
                    'code' => 404,
                    'message' => '文章不存在',
                    'data' => null
                ], 404);
            }
            
            $articleArray = $article->toArray();
            if (isset($article->customFields)) {
                $articleArray['custom_fields'] = $article->customFields;
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $articleArray
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取文章详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建文章
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '创建文章需要管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'category_id', 'title', 'slug', 'summary', 'content', 'author',
                'cover_image', 'status', 'is_top', 'sort_order',
                'seo_title', 'seo_keywords', 'seo_description', 'publish_time',
                'custom_fields'
            ]);
            
            $article = (new ArticleService())->create($data);
            
            $articleArray = $article->toArray();
            if (isset($article->customFields)) {
                $articleArray['custom_fields'] = $article->customFields;
            }
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $articleArray
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建文章失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新文章
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '更新文章需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('文章ID不能为空');
            }
            
            $data = $request->only([
                'category_id', 'title', 'slug', 'summary', 'content', 'author',
                'cover_image', 'status', 'is_top', 'sort_order',
                'seo_title', 'seo_keywords', 'seo_description', 'publish_time',
                'custom_fields'
            ]);
            
            $article = (new ArticleService())->update($id, $data);
            
            $articleArray = $article->toArray();
            if (isset($article->customFields)) {
                $articleArray['custom_fields'] = $article->customFields;
            }
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $articleArray
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新文章失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除文章
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(roles: ['super_admin'], message: '删除文章需要超级管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('文章ID不能为空');
            }
            
            $result = (new ArticleService())->delete($id);
            
            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除文章失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换置顶状态
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '切换置顶状态需要管理员权限')]
    public function toggleTop(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('文章ID不能为空');
            }
            
            $result = (new ArticleService())->toggleTop($id);
            
            return json([
                'code' => 200,
                'message' => '操作成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换置顶状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 切换文章状态
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '切换文章状态需要管理员权限')]
    public function changeStatus(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            $status = $request->param('status');
            
            if (!$id) {
                throw new ValidateException('文章ID不能为空');
            }
            if (empty($status)) {
                throw new ValidateException('状态不能为空');
            }
            
            $result = (new ArticleService())->changeStatus($id, $status);
            
            return json([
                'code' => 200,
                'message' => '操作成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换文章状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量更新排序
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '更新排序需要管理员权限')]
    public function updateSort(Request $request): Response
    {
        try {
            $sortData = $request->param('sort_data', []);
            if (empty($sortData) || !is_array($sortData)) {
                throw new ValidateException('排序数据不能为空');
            }
            
            $result = (new ArticleService())->updateSort($sortData);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => ['result' => $result]
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新排序失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取文章统计信息
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '查看统计信息需要管理员权限')]
    public function statistics(Request $request): Response
    {
        try {
            $statistics = (new ArticleService())->getStatistics();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $statistics
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计信息失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    // ==================== 公开接口（无需权限） ====================

    /**
     * 获取公开文章列表
     */
    public function publicIndex(): Response
    {
        try {
            $page = (int)(request()->get('page', 1));
            $limit = (int)(request()->get('limit', 10));
            $categoryId = request()->get('category_id');

            // 构建查询条件
            $where = ['status' => 'published']; // 只获取已发布的文章
            if ($categoryId) {
                $where['category_id'] = (int)$categoryId;
            }

            $result = (new ArticleRepository())->findWithPagination($where, $page, $limit);

            return json([
                'status' => 'success',
                'message' => '获取文章列表成功',
                'data' => [
                    'articles' => array_map(function($article) {
                        return $article->toArray();
                    }, $result['list']),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $result['total'],
                        'last_page' => $result['pages']
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章列表失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取公开单篇文章
     */
    public function publicRead($id): Response
    {
        try {
            $article = (new ArticleRepository())->findById((int)$id);

            if (!$article || $article->status !== 'published') {
                return json([
                    'status' => 'error',
                    'message' => '文章不存在',
                    'data' => null
                ], 404);
            }

            return json([
                'status' => 'success',
                'message' => '获取文章成功',
                'data' => $article->toArray()
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取热门文章
     */
    public function hot(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 10));

            $where = ['status' => 'published'];
            $result = (new ArticleRepository())->findWithPagination($where, 1, $limit);

            // 按浏览量排序（这里简化处理，实际应该在Repository中实现）
            $articles = $result['list'];
            usort($articles, function($a, $b) {
                return ($b->viewCount ?? 0) - ($a->viewCount ?? 0);
            });

            return json([
                'status' => 'success',
                'message' => '获取热门文章成功',
                'data' => [
                    'articles' => array_map(function($article) {
                        return $article->toArray();
                    }, $articles),
                    'total' => count($articles),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取热门文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取最新文章
     */
    public function latest(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 12));

            $where = ['status' => 'published'];
            $result = (new ArticleRepository())->findWithPagination($where, 1, $limit);

            return json([
                'status' => 'success',
                'message' => '获取最新文章成功',
                'data' => [
                    'articles' => array_map(function($article) {
                        return $article->toArray();
                    }, $result['list']),
                    'total' => count($result['list']),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取最新文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取轮播文章
     */
    public function banner(): Response
    {
        try {
            $limit = (int)(request()->get('limit', 5));

            $where = ['status' => 'published', 'is_top' => 1];
            $result = (new ArticleRepository())->findWithPagination($where, 1, $limit);

            return json([
                'status' => 'success',
                'message' => '获取轮播文章成功',
                'data' => [
                    'articles' => array_map(function($article) {
                        return $article->toArray();
                    }, $result['list']),
                    'total' => count($result['list']),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取轮播文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 搜索文章
     */
    public function search(): Response
    {
        try {
            $keyword = request()->get('keyword', '');
            $page = (int)(request()->get('page', 1));
            $limit = (int)(request()->get('limit', 10));

            if (empty($keyword)) {
                return json([
                    'status' => 'error',
                    'message' => '搜索关键词不能为空'
                ], 400);
            }

            $where = ['status' => 'published', 'title' => $keyword];
            $result = (new ArticleRepository())->findWithPagination($where, $page, $limit);

            return json([
                'status' => 'success',
                'message' => '搜索文章成功',
                'data' => [
                    'articles' => array_map(function($article) {
                        return $article->toArray();
                    }, $result['list']),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $result['total'],
                        'last_page' => $result['pages']
                    ],
                    'keyword' => $keyword,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return json([
                'status' => 'error',
                'message' => '搜索文章失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
