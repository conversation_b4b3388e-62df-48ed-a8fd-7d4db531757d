<?php
declare(strict_types=1);

namespace app\api\controller;

use app\index\controller\Base;
use think\Response;
use Exception;

/**
 * 缓存管理控制器
 */
class CacheController
{
    /**
     * 清除所有缓存
     */
    public function clearAll(): Response
    {
        try {
            // 检查是否为调试模式
            if (!app()->isDebug()) {
                return json([
                    'code' => 403,
                    'message' => '此功能仅在调试模式下可用',
                    'data' => null
                ], 403);
            }
            
            // 清除所有缓存
            Base::clearAllCache();
            
            return json([
                'code' => 200,
                'message' => '缓存清除成功',
                'data' => null
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 清除指定类型的缓存
     */
    public function clearType(): Response
    {
        try {
            // 检查是否为调试模式
            if (!app()->isDebug()) {
                return json([
                    'code' => 403,
                    'message' => '此功能仅在调试模式下可用',
                    'data' => null
                ], 403);
            }
            
            $type = request()->param('type', '');
            
            switch ($type) {
                case 'site_configs':
                    Base::clearSiteConfigsCache();
                    break;
                case 'categories':
                    Base::clearCategoriesCache();
                    break;
                case 'modules':
                    Base::clearPortalModulesCache();
                    break;
                case 'banners':
                    Base::clearPortalBannersCache();
                    break;
                case 'links':
                    Base::clearPortalLinksCache();
                    break;
                case 'ads':
                    $position = request()->param('position', '');
                    Base::clearPortalAdsCache($position);
                    break;
                default:
                    return json([
                        'code' => 400,
                        'message' => '无效的缓存类型',
                        'data' => null
                    ], 400);
            }
            
            return json([
                'code' => 200,
                'message' => '缓存清除成功',
                'data' => null
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取缓存状态
     */
    public function status(): Response
    {
        try {
            // 检查是否为调试模式
            if (!app()->isDebug()) {
                return json([
                    'code' => 403,
                    'message' => '此功能仅在调试模式下可用',
                    'data' => null
                ], 403);
            }
            
            $cacheKeys = [
                'site_configs' => Base::CACHE_SITE_CONFIGS,
                'categories_tree' => Base::CACHE_CATEGORIES,
                'categories_list' => 'categories_list',
                'portal_modules' => Base::CACHE_PORTAL_MODULES,
                'portal_banners' => Base::CACHE_PORTAL_BANNERS,
                'portal_links' => Base::CACHE_PORTAL_LINKS,
            ];
            
            $stats = [];
            foreach ($cacheKeys as $name => $key) {
                $stats[$name] = [
                    'exists' => cache($key) !== null,
                    'key' => $key
                ];
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取缓存状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
