<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\bean\PortalLinkBean;
use app\common\repository\PortalLinkRepository;
use app\common\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;
use Exception;

/**
 * 底部链接 API控制器
 */
class PortalLinkController
{
    private PortalLinkRepository $linkRepository;
    
    public function __construct()
    {
        $this->linkRepository = new PortalLinkRepository();
    }
    
    /**
     * 获取链接列表
     */
    #[RequireRole(message: '获取链接列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 20);
            $categoryName = $request->param('category_name', '');
            $linkTitle = $request->param('link_title', '');
            $isEnabled = $request->param('is_enabled');
            
            $conditions = [];
            if (!empty($categoryName)) {
                $conditions['category_name'] = $categoryName;
            }
            if (!empty($linkTitle)) {
                $conditions['link_title'] = $linkTitle;
            }
            if ($isEnabled !== null) {
                $conditions['is_enabled'] = (int)$isEnabled;
            }
            
            $result = $this->linkRepository->findWithPagination($conditions, $page, $limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取链接列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取启用的链接列表（前台使用）
     */
    public function enabled(Request $request): Response
    {
        try {
            $grouped = $this->linkRepository->findEnabledByCategory();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => array_map(function($links) {
                    return array_map(function($link) {
                        return $link->toArray();
                    }, $links);
                }, $grouped)
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取链接列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取分类列表
     */
    #[RequireRole(message: '获取分类列表需要管理员权限')]
    public function categories(Request $request): Response
    {
        try {
            $categories = $this->linkRepository->getCategories();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $categories
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分类列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取链接详情
     */
    #[RequireRole(message: '获取链接详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('链接ID不能为空');
            }
            
            $link = $this->linkRepository->findById($id);
            if (!$link) {
                throw new ValidateException('链接不存在');
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $link->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取链接详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建链接
     */
    #[RequireRole(message: '创建链接需要管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'category_name', 'link_title', 'link_url', 'description',
                'sort_order', 'category_sort', 'is_enabled', 'target_type', 'is_nofollow'
            ]);
            
            $link = new PortalLinkBean();
            $link->categoryName = $data['category_name'] ?? '';
            $link->linkTitle = $data['link_title'] ?? '';
            $link->linkUrl = $data['link_url'] ?? '';
            $link->description = $data['description'] ?? '';
            $link->sortOrder = (int)($data['sort_order'] ?? 0);
            $link->categorySort = (int)($data['category_sort'] ?? 0);
            $link->isEnabled = (int)($data['is_enabled'] ?? 1);
            $link->targetType = $data['target_type'] ?? '_blank';
            $link->isNofollow = (int)($data['is_nofollow'] ?? 0);
            
            $result = $this->linkRepository->create($link);
            
            // 清除相关缓存
            cache('portal_links', null);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建链接失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新链接
     */
    #[RequireRole(message: '更新链接需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('链接ID不能为空');
            }
            
            $link = $this->linkRepository->findById($id);
            if (!$link) {
                throw new ValidateException('链接不存在');
            }
            
            $data = $request->only([
                'category_name', 'link_title', 'link_url', 'description',
                'sort_order', 'category_sort', 'is_enabled', 'target_type', 'is_nofollow'
            ]);
            
            $link->categoryName = $data['category_name'] ?? $link->categoryName;
            $link->linkTitle = $data['link_title'] ?? $link->linkTitle;
            $link->linkUrl = $data['link_url'] ?? $link->linkUrl;
            $link->description = $data['description'] ?? $link->description;
            $link->sortOrder = (int)($data['sort_order'] ?? $link->sortOrder);
            $link->categorySort = (int)($data['category_sort'] ?? $link->categorySort);
            $link->isEnabled = (int)($data['is_enabled'] ?? $link->isEnabled);
            $link->targetType = $data['target_type'] ?? $link->targetType;
            $link->isNofollow = (int)($data['is_nofollow'] ?? $link->isNofollow);
            
            $result = $this->linkRepository->update($link);
            
            // 清除相关缓存
            cache('portal_links', null);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新链接失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除链接
     */
    #[RequireRole(message: '删除链接需要管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('链接ID不能为空');
            }
            
            $link = $this->linkRepository->findById($id);
            if (!$link) {
                throw new ValidateException('链接不存在');
            }
            
            $result = $this->linkRepository->delete($id);
            
            if ($result) {
                // 清除相关缓存
                cache('portal_links', null);
                
                return json([
                    'code' => 200,
                    'message' => '删除成功',
                    'data' => null
                ]);
            } else {
                throw new Exception('删除失败');
            }
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除链接失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 链接点击统计
     */
    public function click(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('链接ID不能为空');
            }
            
            $this->linkRepository->updateClickCount($id);
            
            return json([
                'code' => 200,
                'message' => '统计成功',
                'data' => null
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
