<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\bean\PortalAdBean;
use app\common\repository\PortalAdRepository;
use app\common\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;
use Exception;

/**
 * 广告位 API控制器
 */
class PortalAdController
{
    private PortalAdRepository $adRepository;
    
    public function __construct()
    {
        $this->adRepository = new PortalAdRepository();
    }
    
    /**
     * 获取广告列表
     */
    #[RequireRole(message: '获取广告列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 20);
            $adPosition = $request->param('ad_position', '');
            $adTitle = $request->param('ad_title', '');
            $adType = $request->param('ad_type', '');
            $isEnabled = $request->param('is_enabled');
            
            $conditions = [];
            if (!empty($adPosition)) {
                $conditions['ad_position'] = $adPosition;
            }
            if (!empty($adTitle)) {
                $conditions['ad_title'] = $adTitle;
            }
            if (!empty($adType)) {
                $conditions['ad_type'] = $adType;
            }
            if ($isEnabled !== null) {
                $conditions['is_enabled'] = (int)$isEnabled;
            }
            
            $result = $this->adRepository->findWithPagination($conditions, $page, $limit);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取广告列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 根据广告位获取广告（前台使用）
     */
    public function position(Request $request): Response
    {
        try {
            $position = $request->param('position', '');
            if (empty($position)) {
                throw new ValidateException('广告位标识不能为空');
            }
            
            $ads = $this->adRepository->findDisplayableByPosition($position);
            
            // 增加展示次数统计
            foreach ($ads as $ad) {
                $this->adRepository->updateViewCount($ad->id);
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => array_map(function($ad) {
                    return $ad->toArray();
                }, $ads)
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取广告失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取广告详情
     */
    #[RequireRole(message: '获取广告详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('广告ID不能为空');
            }
            
            $ad = $this->adRepository->findById($id);
            if (!$ad) {
                throw new ValidateException('广告不存在');
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $ad->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取广告详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建广告
     */
    #[RequireRole(message: '创建广告需要管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'ad_position', 'ad_title', 'ad_image', 'ad_url', 'ad_content',
                'ad_type', 'width', 'height', 'sort_order', 'is_enabled',
                'target_type', 'start_time', 'end_time'
            ]);
            
            $ad = new PortalAdBean();
            $ad->adPosition = $data['ad_position'] ?? '';
            $ad->adTitle = $data['ad_title'] ?? '';
            $ad->adImage = $data['ad_image'] ?? '';
            $ad->adUrl = $data['ad_url'] ?? '';
            $ad->adContent = $data['ad_content'] ?? null;
            $ad->adType = $data['ad_type'] ?? 'image';
            $ad->width = (int)($data['width'] ?? 300);
            $ad->height = (int)($data['height'] ?? 250);
            $ad->sortOrder = (int)($data['sort_order'] ?? 0);
            $ad->isEnabled = (int)($data['is_enabled'] ?? 1);
            $ad->targetType = $data['target_type'] ?? '_blank';
            $ad->startTime = $data['start_time'] ?? null;
            $ad->endTime = $data['end_time'] ?? null;
            
            $result = $this->adRepository->create($ad);
            
            // 清除相关缓存
            cache('portal_ads_' . $ad->adPosition, null);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建广告失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新广告
     */
    #[RequireRole(message: '更新广告需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('广告ID不能为空');
            }
            
            $ad = $this->adRepository->findById($id);
            if (!$ad) {
                throw new ValidateException('广告不存在');
            }
            
            $oldPosition = $ad->adPosition;
            
            $data = $request->only([
                'ad_position', 'ad_title', 'ad_image', 'ad_url', 'ad_content',
                'ad_type', 'width', 'height', 'sort_order', 'is_enabled',
                'target_type', 'start_time', 'end_time'
            ]);
            
            $ad->adPosition = $data['ad_position'] ?? $ad->adPosition;
            $ad->adTitle = $data['ad_title'] ?? $ad->adTitle;
            $ad->adImage = $data['ad_image'] ?? $ad->adImage;
            $ad->adUrl = $data['ad_url'] ?? $ad->adUrl;
            $ad->adContent = $data['ad_content'] ?? $ad->adContent;
            $ad->adType = $data['ad_type'] ?? $ad->adType;
            $ad->width = (int)($data['width'] ?? $ad->width);
            $ad->height = (int)($data['height'] ?? $ad->height);
            $ad->sortOrder = (int)($data['sort_order'] ?? $ad->sortOrder);
            $ad->isEnabled = (int)($data['is_enabled'] ?? $ad->isEnabled);
            $ad->targetType = $data['target_type'] ?? $ad->targetType;
            $ad->startTime = $data['start_time'] ?? $ad->startTime;
            $ad->endTime = $data['end_time'] ?? $ad->endTime;
            
            $result = $this->adRepository->update($ad);
            
            // 清除相关缓存
            cache('portal_ads_' . $oldPosition, null);
            if ($oldPosition !== $ad->adPosition) {
                cache('portal_ads_' . $ad->adPosition, null);
            }
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $result->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新广告失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除广告
     */
    #[RequireRole(message: '删除广告需要管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('广告ID不能为空');
            }
            
            $ad = $this->adRepository->findById($id);
            if (!$ad) {
                throw new ValidateException('广告不存在');
            }
            
            $result = $this->adRepository->delete($id);
            
            if ($result) {
                // 清除相关缓存
                cache('portal_ads_' . $ad->adPosition, null);
                
                return json([
                    'code' => 200,
                    'message' => '删除成功',
                    'data' => null
                ]);
            } else {
                throw new Exception('删除失败');
            }
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除广告失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 广告点击统计
     */
    public function click(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (empty($id)) {
                throw new ValidateException('广告ID不能为空');
            }
            
            $this->adRepository->updateClickCount($id);
            
            return json([
                'code' => 200,
                'message' => '统计成功',
                'data' => null
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
