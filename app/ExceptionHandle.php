<?php
namespace app;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use think\facade\View;
use Throwable;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 判断是否为AJAX请求
        if ($request->isAjax()) {
            return $this->renderAjaxError($e);
        }

        // 处理HTTP异常
        if ($e instanceof HttpException) {
            return $this->renderHttpError($request, $e);
        }

        // 处理其他异常
        return $this->renderGeneralError($request, $e);
    }

    /**
     * 渲染AJAX请求错误
     *
     * @param Throwable $e
     * @return Response
     */
    protected function renderAjaxError(Throwable $e): Response
    {
        $statusCode = 500;
        $message = '系统错误';

        if ($e instanceof HttpException) {
            $statusCode = $e->getStatusCode();
            $message = $this->getErrorMessage($statusCode);
        }

        return json([
            'code' => $statusCode,
            'message' => $message,
            'data' => null
        ], $statusCode);
    }

    /**
     * 渲染HTTP错误页面
     *
     * @param \think\Request $request
     * @param HttpException $e
     * @return Response
     */
    protected function renderHttpError($request, HttpException $e): Response
    {
        $statusCode = $e->getStatusCode();
        $message = $e->getMessage() ?: $this->getErrorMessage($statusCode);

        // 输出详细错误信息到控制台和日志文件
        $errorInfo = [
            "=== HTTP错误 {$statusCode} ===",
            "时间: " . date('Y-m-d H:i:s'),
            "错误信息: " . $message,
            "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行",
            "错误堆栈:\n" . $e->getTraceAsString(),
            "请求URL: " . $request->url(true),
            "请求方法: " . $request->method(),
            "==================="
        ];

        foreach ($errorInfo as $line) {
            error_log($line);
        }

        // 同时写入到自定义日志文件
        $logFile = runtime_path() . 'log' . DIRECTORY_SEPARATOR . 'error_detail.log';
        file_put_contents($logFile, implode("\n", $errorInfo) . "\n\n", FILE_APPEND | LOCK_EX);

        // 准备错误页面数据
        $errorData = [
            'error_code' => $statusCode,
            'error_title' => $this->getErrorTitle($statusCode),
            'error_message' => $message,
            'debug_info' => app()->isDebug() ? [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_url' => $request->url(true),
                'request_method' => $request->method()
            ] : null
        ];

        // 统一使用 error/index 模板
        try {
            View::assign($errorData);
            $content = View::fetch('error/index');
            return response($content, $statusCode);
        } catch (\Exception $ex) {
            // 如果模板渲染失败，返回简单错误信息
            return response($this->getSimpleErrorPage($statusCode, $message), $statusCode);
        }
    }

    /**
     * 渲染一般错误页面
     *
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    protected function renderGeneralError($request, Throwable $e): Response
    {
        // 记录错误日志
        $this->report($e);

        // 输出详细错误信息到控制台和日志文件
        $errorInfo = [
            "=== 系统错误 ===",
            "时间: " . date('Y-m-d H:i:s'),
            "错误信息: " . $e->getMessage(),
            "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行",
            "错误堆栈:\n" . $e->getTraceAsString(),
            "请求URL: " . $request->url(true),
            "请求方法: " . $request->method(),
            "==============="
        ];

        foreach ($errorInfo as $line) {
            error_log($line);
        }

        // 同时写入到自定义日志文件
        $logFile = runtime_path() . 'log' . DIRECTORY_SEPARATOR . 'error_detail.log';
        file_put_contents($logFile, implode("\n", $errorInfo) . "\n\n", FILE_APPEND | LOCK_EX);

        $errorData = [
            'error_code' => 500,
            'error_title' => '系统错误',
            'error_message' => app()->isDebug() ? $e->getMessage() : '系统遇到了一些问题，请稍后再试',
            'debug_info' => app()->isDebug() ? [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_url' => $request->url(true),
                'request_method' => $request->method()
            ] : null
        ];

        try {
            View::assign($errorData);
            $content = View::fetch('error/index');
            return response($content, 500);
        } catch (\Exception $ex) {
            // 如果模板渲染失败，返回简单错误信息
            return response($this->getSimpleErrorPage(500, $errorData['error_message']), 500);
        }
    }

    /**
     * 获取错误信息
     *
     * @param int $statusCode
     * @return string
     */
    protected function getErrorMessage(int $statusCode): string
    {
        $messages = [
            400 => '请求参数错误',
            401 => '未授权访问',
            403 => '访问被拒绝',
            404 => '页面不存在',
            405 => '请求方法不允许',
            500 => '服务器内部错误',
            502 => '网关错误',
            503 => '服务不可用',
            504 => '网关超时'
        ];

        return $messages[$statusCode] ?? '未知错误';
    }

    /**
     * 获取错误标题
     *
     * @param int $statusCode
     * @return string
     */
    protected function getErrorTitle(int $statusCode): string
    {
        $titles = [
            400 => '请求错误',
            401 => '未授权',
            403 => '访问被拒绝',
            404 => '页面不存在',
            405 => '方法不允许',
            500 => '服务器错误',
            502 => '网关错误',
            503 => '服务不可用',
            504 => '网关超时'
        ];

        return $titles[$statusCode] ?? '系统错误';
    }



    /**
     * 获取简单错误页面HTML
     *
     * @param int $statusCode
     * @param string $message
     * @return string
     */
    protected function getSimpleErrorPage(int $statusCode, string $message): string
    {
        return "<!DOCTYPE html>
<html>
<head>
    <meta charset=\"utf-8\">
    <title>错误 {$statusCode}</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .error-container { max-width: 500px; margin: 0 auto; }
        .error-code { font-size: 72px; color: #ff5722; margin: 0; }
        .error-message { font-size: 18px; color: #666; margin: 20px 0; }
        .error-link { color: #1e9fff; text-decoration: none; }
    </style>
</head>
<body>
    <div class=\"error-container\">
        <h1 class=\"error-code\">{$statusCode}</h1>
        <p class=\"error-message\">{$message}</p>
        <a href=\"/\" class=\"error-link\">返回首页</a>
    </div>
</body>
</html>";
    }
}
