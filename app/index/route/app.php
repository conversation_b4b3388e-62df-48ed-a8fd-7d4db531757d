<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 门户首页路由 - 根目录直接显示门户页面
Route::group('/', function () { // 注意这里，将 '/' 改为 'index'
    Route::get('/', 'Index/index');
    Route::get('category/:id', 'Index/category');
    Route::get('article/:id', 'Index/article');
    Route::get('search', 'Index/search');
})->pattern([
    'id' => '\d+'
]);

// 错误页面测试（仅在调试模式下可用）
if (app()->isDebug()) {
    Route::group('error-test', function () {
        Route::get('new-error', 'ErrorTest/testNewError');
        Route::get('404', 'ErrorTest/test404');
        Route::get('403', 'ErrorTest/test403');
    });
}
