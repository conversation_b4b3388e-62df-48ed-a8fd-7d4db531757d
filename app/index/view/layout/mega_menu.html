<!-- 华为云风格的全屏悬浮菜单 -->
<nav class="mega-menu">
    <div class="layui-container">
        <div class="primary-menu">
            <a href="/" class="nav-item {empty name='currentCategoryId'}active{/empty}">首页</a>
            
            {volist name="menuTree" id="topCategory"}
            <div class="nav-item-wrapper">
                <a href="/category/{$topCategory.id}" class="nav-item {if $currentCategoryId == $topCategory.id}active{/if}">
                    {$topCategory.name}
                    {if !empty($topCategory.children)}
                    <i class="layui-icon layui-icon-down"></i>
                    {/if}
                </a>
                
                {if !empty($topCategory.children)}
                <!-- 遮罩层 -->
                <div class="menu-overlay"></div>

                <!-- 二级菜单容器 -->
                <div class="submenu-container">
                    <div class="layui-container">
                        <div class="layui-row">
                            {volist name="topCategory.children" id="secondCategory"}
                            <div class="layui-col-md3">
                                <div class="submenu-group">
                                    <h4 class="submenu-title">
                                        <a href="/category/{$secondCategory.id}">{$secondCategory.name}</a>
                                    </h4>

                                    {if !empty($secondCategory.children)}
                                    <ul class="submenu-list">
                                        {volist name="secondCategory.children" id="thirdCategory"}
                                        <li>
                                            <a href="/category/{$thirdCategory.id}">{$thirdCategory.name}</a>
                                        </li>
                                        {/volist}
                                    </ul>
                                    {/if}
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                </div>
                {/if}
            </div>
            {/volist}
        </div>
    </div>
</nav>

<style>
/* 华为云风格的全屏悬浮菜单样式 */
.mega-menu {
    background-color: #252b3a;
    color: #fff;
    position: relative;
    z-index: 1000;
}

.mega-menu .primary-menu {
    display: flex;
    height: 50px;
    align-items: center;
}

.mega-menu .nav-item {
    color: #fff;
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    display: inline-block;
    text-decoration: none;
    font-size: 16px;
    position: relative;
    transition: all 0.3s;
}

.mega-menu .nav-item:hover,
.mega-menu .nav-item.active {
    color: #fff;
    background-color: #0256c9;
}

.mega-menu .nav-item-wrapper {
    position: relative;
}

.mega-menu .nav-item-wrapper:hover .submenu-container {
    display: block;
}

.mega-menu .nav-item-wrapper:hover .menu-overlay {
    display: block;
}

.mega-menu .submenu-container {
    display: none;
    position: fixed;
    left: 0;
    top: 100%;
    width: 100vw;
    background-color: #fff;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    padding: 30px 0;
    z-index: 1001;
}

.mega-menu .menu-overlay {
    display: none;
    position: fixed;
    top: 100%;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

.mega-menu .submenu-group {
    margin-bottom: 20px;
}

.mega-menu .submenu-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #252b3a;
}

.mega-menu .submenu-title a {
    color: #252b3a;
    text-decoration: none;
}

.mega-menu .submenu-title a:hover {
    color: #0256c9;
}

.mega-menu .submenu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mega-menu .submenu-list li {
    margin-bottom: 8px;
}

.mega-menu .submenu-list a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    display: block;
    padding: 5px 0;
}

.mega-menu .submenu-list a:hover {
    color: #0256c9;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .mega-menu .submenu-container {
        position: static;
        display: none;
        padding: 10px 0;
    }
    
    .mega-menu .primary-menu {
        flex-direction: column;
        height: auto;
        align-items: stretch;
    }
    
    .mega-menu .nav-item-wrapper {
        width: 100%;
    }
    
    .mega-menu .nav-item {
        width: 100%;
        box-sizing: border-box;
    }
}
</style>

<script>
// 菜单交互脚本 - 延迟执行确保layui已加载
document.addEventListener('DOMContentLoaded', function() {
    // 如果layui还未加载，等待加载
    if (typeof layui === 'undefined') {
        setTimeout(function() {
            initMegaMenu();
        }, 100);
    } else {
        initMegaMenu();
    }
});

function initMegaMenu() {
    if (typeof layui !== 'undefined') {
        layui.use(['jquery'], function(){
            var $ = layui.jquery;

            // 移动端菜单展开/收起
            $('.nav-item-wrapper > .nav-item').on('click', function(e){
                if($(window).width() <= 768 && $(this).siblings('.submenu-container').length) {
                    e.preventDefault();
                    $(this).siblings('.submenu-container').slideToggle();
                }
            });

            // 窗口大小变化时重置菜单状态
            $(window).resize(function(){
                if($(window).width() > 768) {
                    $('.submenu-container').css('display', '');
                }
            });
        });
    }
}
</script>
