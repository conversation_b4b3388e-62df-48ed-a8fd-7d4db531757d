<script src="/static/layui/layui.js"></script>
<script>
    layui.use(['carousel', 'element'], function () {
        var carousel = layui.carousel;
        var element = layui.element;

        // 初始化轮播
        if (document.getElementById('newsBanner')) {
            carousel.render({
                elem: '#newsBanner',
                width: '100%',
                height: '400px',
                interval: 5000,
                anim: 'fade'
            });
        }
    });

    // 更新时间
    function updateTime() {
        const now = new Date();
        const timeStr = now.getFullYear() + '年' +
            (now.getMonth() + 1).toString().padStart(2, '0') + '月' +
            now.getDate().toString().padStart(2, '0') + '日 ' +
            now.getHours().toString().padStart(2, '0') + ':' +
            now.getMinutes().toString().padStart(2, '0');
        document.getElementById('currentTime').textContent = timeStr;
    }

    // 搜索功能
    function searchNews() {
        const keyword = document.getElementById('searchInput').value.trim();
        if (keyword) {
            window.location.href = '/search?q=' + encodeURIComponent(keyword);
        }
    }

    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            searchNews();
        }
    });

    // Banner点击统计
    function trackBannerClick(bannerId) {
        fetch('/api/banners/click/' + bannerId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        }).catch(error => {
            console.log('Banner点击统计失败:', error);
        });
    }

    // 广告点击统计
    function trackAdClick(adId) {
        fetch('/api/ads/click/' + adId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        }).catch(error => {
            console.log('广告点击统计失败:', error);
        });
    }

    // 链接点击统计
    function trackLinkClick(linkId) {
        fetch('/api/links/click/' + linkId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        }).catch(error => {
            console.log('链接点击统计失败:', error);
        });
    }

    // 缓存清除功能（仅调试模式）
    {if app()->isDebug()}
    function clearAllCache() {
        const btn = document.getElementById('clearCacheBtn');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 清理中...';

            fetch('/api/cache/clear-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    layui.use('layer', function(){
                        var layer = layui.layer;
                        layer.msg('缓存清除成功！', {icon: 1, time: 2000});
                    });
                } else {
                    layui.use('layer', function(){
                        var layer = layui.layer;
                        layer.msg('缓存清除失败：' + data.message, {icon: 2, time: 3000});
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                layui.use('layer', function(){
                    var layer = layui.layer;
                    layer.msg('网络错误，请稍后重试', {icon: 2, time: 3000});
                });
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="layui-icon layui-icon-delete"></i> 清缓存';
            });
        }
    }

    // 绑定清除缓存按钮事件
    document.addEventListener('DOMContentLoaded', function() {
        const clearBtn = document.getElementById('clearCacheBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', clearAllCache);
        }
    });
    {/if}

    // 初始化
    updateTime();
    setInterval(updateTime, 60000); // 每分钟更新一次时间
</script>