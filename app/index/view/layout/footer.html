<!-- 外链模块 -->
{if !empty($portalLinks)}
<section class="external-links">
    <div class="layui-container">
        <div class="links-header">
            <h3>相关链接</h3>
        </div>
        <div class="links-content">
            {volist name="portalLinks" id="links" key="categoryName"}
            <div class="links-category">
                <div class="category-title">{$categoryName}</div>
                <div class="links-list">
                    {volist name="links" id="link"}
                    <a href="{$link->linkUrl}"
                       {$link->getLinkAttributes()|raw}
                       class="external-link"
                       onclick="trackLinkClick({$link->id})"
                       title="{$link->description}">
                        {$link->linkTitle}
                    </a>
                    {/volist}
                </div>
            </div>
            {/volist}
        </div>
    </div>
</section>
{else}
<!-- 默认链接（兼容） -->
<section class="external-links">
    <div class="layui-container">
        <div class="links-header">
            <h3>相关链接</h3>
        </div>
        <div class="links-content">
            <div class="links-category">
                <div class="category-title">友情链接</div>
                <div class="links-list">
                    <a href="https://www.baidu.com" target="_blank" class="external-link">百度</a>
                    <a href="https://www.tencent.com" target="_blank" class="external-link">腾讯</a>
                    <a href="https://www.alibaba.com" target="_blank" class="external-link">阿里巴巴</a>
                </div>
            </div>
            <div class="links-category">
                <div class="category-title">政府机构</div>
                <div class="links-list">
                    <a href="http://www.gov.cn" target="_blank" class="external-link">中国政府网</a>
                    <a href="http://www.xinhuanet.com" target="_blank" class="external-link">新华网</a>
                </div>
            </div>
        </div>
    </div>
</section>
{/if}

<!-- 底部版权 -->
<footer class="footer">
    <div class="layui-container">
        <div class="footer-bottom">
            <p>&copy; 2025 {$basicConfigs.site_name ?? '新闻门户网站'}. 版权所有 |
               <a href="#" style="color: #999;">使用协议</a> |
               <a href="#" style="color: #999;">隐私政策</a> |
               <a href="#" style="color: #999;">联系我们</a>
            </p>
        </div>
    </div>
</footer>
