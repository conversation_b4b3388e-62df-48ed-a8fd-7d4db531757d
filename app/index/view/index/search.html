{include file="layout/head" /}
<body>
    {include file="layout/header" /}

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="layui-container">
            <div class="content-wrapper">
                <!-- 搜索结果页面 -->
                <div class="search-header">
                    <h1 class="search-title">搜索结果</h1>
                    <p class="search-info">
                        关键词：<strong>"{$keyword}"</strong>
                        {if isset($pagination.total)}
                        ，共找到 <strong>{$pagination.total}</strong> 条结果
                        {/if}
                    </p>
                </div>

                <!-- 搜索结果列表 -->
                {if !empty($articles)}
                <div class="search-results">
                    {volist name="articles" id="article"}
                    <div class="search-item">
                        {if !empty($article->coverImage)}
                        <div class="search-image" style="background-image: url('{$article->coverImage}');"></div>
                        {else}
                        <div class="search-image" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                            <i class="layui-icon layui-icon-picture" style="font-size: 32px; color: #ccc;"></i>
                        </div>
                        {/if}
                        <div class="search-content">
                            <a href="/article/{$article->id}" class="search-article-title">{$article->title}</a>
                            <div class="search-meta">
                                <span><i class="layui-icon layui-icon-time"></i> {$article->publishTime|date='Y-m-d H:i'}</span>
                                <span style="margin-left: 15px;"><i class="layui-icon layui-icon-username"></i> {$article->author ?? '佚名'}</span>
                                <span style="margin-left: 15px;"><i class="layui-icon layui-icon-read"></i> {$article->viewCount ?? 0}</span>
                            </div>
                            {if !empty($article->summary)}
                            <div class="search-summary">{$article->summary}</div>
                            {/if}
                        </div>
                    </div>
                    {/volist}
                </div>

                <!-- 分页 -->
                {if isset($pagination) && $pagination.last_page > 1}
                <div class="pagination-wrapper">
                    <div class="layui-box layui-laypage layui-laypage-default">
                        {if $pagination.current_page > 1}
                        <a href="?keyword={$keyword}&page={$pagination.current_page - 1}" class="layui-laypage-prev">上一页</a>
                        {/if}
                        
                        {for start="1" end="$pagination.last_page"}
                        {if $i == $pagination.current_page}
                        <span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>{$i}</em></span>
                        {else}
                        <a href="?keyword={$keyword}&page={$i}">{$i}</a>
                        {/if}
                        {/for}
                        
                        {if $pagination.current_page < $pagination.last_page}
                        <a href="?keyword={$keyword}&page={$pagination.current_page + 1}" class="layui-laypage-next">下一页</a>
                        {/if}
                    </div>
                </div>
                {/if}
                {else}
                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="layui-icon layui-icon-search" style="font-size: 64px; color: #ccc;"></i>
                    </div>
                    <h3>没有找到相关内容</h3>
                    <p>请尝试使用其他关键词搜索</p>
                    <div class="search-suggestions">
                        <p>搜索建议：</p>
                        <ul>
                            <li>检查关键词拼写是否正确</li>
                            <li>尝试使用更简单的关键词</li>
                            <li>使用同义词或相关词汇</li>
                        </ul>
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </main>

    {include file="layout/footer" /}

    <style>
        .search-header {
            padding: 20px 0;
            border-bottom: 1px solid #e8e8e8;
            margin-bottom: 20px;
        }
        
        .search-title {
            font-size: 24px;
            color: #333;
            margin: 0 0 10px 0;
        }
        
        .search-info {
            color: #666;
            margin: 0;
        }
        
        .search-results {
            margin-bottom: 30px;
        }
        
        .search-item {
            display: flex;
            padding: 20px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .search-image {
            width: 120px;
            height: 80px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .search-content {
            flex: 1;
        }
        
        .search-article-title {
            font-size: 18px;
            color: #333;
            text-decoration: none;
            font-weight: 500;
            line-height: 1.4;
            display: block;
            margin-bottom: 8px;
        }
        
        .search-article-title:hover {
            color: #1890ff;
        }
        
        .search-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 10px;
        }
        
        .search-summary {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
        }
        
        .no-results-icon {
            margin-bottom: 20px;
        }
        
        .no-results h3 {
            color: #333;
            margin: 0 0 10px 0;
        }
        
        .no-results p {
            color: #666;
            margin: 0 0 20px 0;
        }
        
        .search-suggestions {
            text-align: left;
            max-width: 300px;
            margin: 0 auto;
        }
        
        .search-suggestions ul {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }
        
        .search-suggestions li {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .search-suggestions li:before {
            content: "•";
            color: #1890ff;
            position: absolute;
            left: 0;
        }
        
        .pagination-wrapper {
            text-align: center;
            margin-top: 30px;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .search-item {
                flex-direction: column;
            }
            
            .search-image {
                width: 100%;
                height: 150px;
                margin-right: 0;
                margin-bottom: 15px;
            }
        }
    </style>

    {include file="layout/scripts" /}
</body>
</html>
