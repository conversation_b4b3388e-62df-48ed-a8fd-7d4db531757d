{include file="layout/head" /}
<body>
    {include file="layout/header" /}

    <!-- 文章页面特有样式 -->
    <style>
        body { line-height: 1.6; }
        .article-container {
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .article-header {
            padding: 30px;
            border-bottom: 1px solid #f0f0f0;
        }
        .article-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .article-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .article-summary {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #1890ff;
            color: #666;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .article-cover {
            text-align: center;
            padding: 0 30px;
        }
        .article-cover img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .article-content {
            padding: 30px;
            font-size: 16px;
            line-height: 1.8;
            color: #333;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 30px 0 15px 0;
            color: #333;
        }
        .article-content h1 { font-size: 24px; }
        .article-content h2 { font-size: 20px; }
        .article-content h3 { font-size: 18px; }
        .article-content p {
            margin-bottom: 15px;
        }
        .article-content ul, .article-content ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        .article-content li {
            margin-bottom: 8px;
        }
        .article-content blockquote {
            background: #f8f9fa;
            border-left: 4px solid #1890ff;
            padding: 15px 20px;
            margin: 20px 0;
            font-style: italic;
        }
        
        .custom-fields {
            padding: 20px 30px;
            background: #fafafa;
            border-top: 1px solid #f0f0f0;
        }
        .custom-fields-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .custom-field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        .field-value {
            color: #333;
        }
        .field-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .tag-item {
            background: #1890ff;
            color: #fff;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
        }
        .field-link {
            color: #1890ff;
            text-decoration: none;
        }
        .field-link:hover {
            text-decoration: underline;
        }
        
        .related-articles {
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .related-title {
            padding: 20px 30px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }
        .related-list {
            padding: 20px 30px;
        }
        .related-item {
            display: flex;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .related-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .related-image {
            width: 100px;
            height: 70px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .related-content {
            flex: 1;
        }
        .related-article-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            text-decoration: none;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .related-article-title:hover {
            color: #1890ff;
        }
        .related-meta {
            font-size: 12px;
            color: #999;
        }
        
        .footer {
            background: #333;
            color: #fff;
            padding: 20px 0;
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .article-title {
                font-size: 22px;
            }
            .article-header, .article-content, .custom-fields {
                padding: 20px;
            }
            .article-meta {
                flex-direction: column;
                gap: 10px;
            }
            .related-item {
                flex-direction: column;
            }
            .related-image {
                width: 100%;
                height: 150px;
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="layui-container">
            <div class="layui-row layui-col-space20">
                <!-- 文章内容 -->
                <div class="layui-col-md9">
                    <article class="article-container">
                        <!-- 文章头部 -->
                        <header class="article-header">
                            <h1 class="article-title">{$article->title}</h1>
                            <div class="article-meta">
                                <div class="meta-item">
                                    <i class="layui-icon layui-icon-time"></i>
                                    <span>{$article->publishTime|date='Y年m月d日 H:i'}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="layui-icon layui-icon-username"></i>
                                    <span>{$article->author ?? '佚名'}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="layui-icon layui-icon-tabs"></i>
                                    <a href="/category/{$article->categoryId}" style="color: #666;">
                                        {volist name="categories" id="cat"}
                                            {if $cat->id == $article->categoryId}{$cat->name}{/if}
                                        {/volist}
                                    </a>
                                </div>
                                <div class="meta-item">
                                    <i class="layui-icon layui-icon-read"></i>
                                    <span>{$article->viewCount ?? 0} 次阅读</span>
                                </div>
                            </div>
                            {if !empty($article->summary)}
                            <div class="article-summary">{$article->summary}</div>
                            {/if}
                        </header>
                        
                        <!-- 文章封面 -->
                        {if !empty($article->coverImage)}
                        <div class="article-cover">
                            <img src="{$article->coverImage}" alt="{$article->title}">
                        </div>
                        {/if}

                        <!-- 文章正文 -->
                        <div class="article-content">
                            {$article->content|raw}
                        </div>
                        
                        <!-- 自定义字段 -->
                        {if !empty($customFields)}
                        <div class="custom-fields">
                            <div class="custom-fields-title">相关信息</div>
                            {volist name="customFields" id="field" key="fieldKey"}
                            <div class="custom-field">
                                <div class="field-label">{$field.name}：</div>
                                <div class="field-value">
                                    {switch name="field.type"}
                                        {case value="tag"}
                                            {if is_array($field.value)}
                                            <div class="field-tags">
                                                {volist name="field.value" id="tag"}
                                                <span class="tag-item">{$tag}</span>
                                                {/volist}
                                            </div>
                                            {else}
                                            {$field.value}
                                            {/if}
                                        {/case}
                                        {case value="text"}
                                            {if $fieldKey == 'external_link' && !empty($field.value)}
                                            <a href="{$field.value}" target="_blank" class="field-link">{$field.value}</a>
                                            {else}
                                            {$field.value}
                                            {/if}
                                        {/case}
                                        {case value="textarea"}
                                            <div style="white-space: pre-wrap;">{$field.value}</div>
                                        {/case}
                                        {default /}
                                            {$field.value}
                                    {/switch}
                                </div>
                            </div>
                            {/volist}
                        </div>
                        {/if}
                    </article>
                </div>
                
                <!-- 侧边栏 -->
                <div class="layui-col-md3">
                    <!-- 相关文章 -->
                    {if !empty($relatedArticles)}
                    <div class="related-articles">
                        <div class="related-title">相关文章</div>
                        <div class="related-list">
                            {volist name="relatedArticles" id="related"}
                            <div class="related-item">
                                {if !empty($related->coverImage)}
                                <div class="related-image" style="background-image: url('{$related->coverImage}');"></div>
                                {else}
                                <div class="related-image" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                                    <i class="layui-icon layui-icon-picture" style="font-size: 20px; color: #ccc;"></i>
                                </div>
                                {/if}
                                <div class="related-content">
                                    <a href="/article/{$related->id}" class="related-article-title">{$related->title}</a>
                                    <div class="related-meta">
                                        <span>{$related->publishTime|date='m-d H:i'}</span>
                                        <span style="margin-left: 10px;">{$related->viewCount ?? 0}阅读</span>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </main>

    {include file="layout/footer" /}

    {include file="layout/scripts" /}

    <!-- 文章页面特有脚本 -->
    <script>
        // 代码高亮等功能可以在这里添加
        document.addEventListener('DOMContentLoaded', function() {
            // 为外部链接添加图标
            const externalLinks = document.querySelectorAll('a[href^="http"]');
            externalLinks.forEach(link => {
                if (!link.hostname.includes(window.location.hostname)) {
                    link.innerHTML += ' <i class="layui-icon layui-icon-link" style="font-size: 12px;"></i>';
                }
            });
        });
    </script>
</body>
</html>
