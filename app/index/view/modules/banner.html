<!-- 新闻轮播模块 -->
{if !empty($portalBanners)}
<div class="layui-carousel" id="newsBanner">
    <div carousel-item>
        {volist name="portalBanners" id="banner"}
        <div class="banner-item" style="background-image: url('{$banner->imageUrl}');">
            <div class="banner-content">
                <div class="banner-title">
                    <a href="{$banner->linkUrl}" style="color: #fff; text-decoration: none;"
                       target="{$banner->targetType}"
                       onclick="trackBannerClick({$banner->id})">
                        {$banner->title}
                    </a>
                </div>
                {if !empty($banner->description)}
                <div class="banner-summary">{$banner->description}</div>
                {/if}
            </div>
        </div>
        {/volist}
    </div>
</div>
{elseif !empty($newsData['banner'])}
<!-- 兼容旧的新闻轮播数据 -->
<div class="layui-carousel" id="newsBanner">
    <div carousel-item>
        {volist name="newsData.banner" id="news"}
        <div class="banner-item" style="background-image: url('{$news->coverImage}');">
            <div class="banner-content">
                <div class="banner-title">
                    <a href="/article/{$news->id}" style="color: #fff; text-decoration: none;">
                        {$news->title}
                    </a>
                </div>
                {if !empty($news->summary)}
                <div class="banner-summary">{$news->summary}</div>
                {/if}
            </div>
        </div>
        {/volist}
    </div>
</div>
{/if}
