<!-- 热门新闻模块 -->
<div class="module-header">
    <i class="layui-icon layui-icon-fire" style="color: #ff5722; margin-right: 8px;"></i>
    {$module->moduleTitle}
</div>
<div class="module-body">
    {if !empty($newsData['hot'])}
    <ul class="news-list">
        {volist name="newsData.hot" id="news" key="index"}
        <li class="news-item">
            {if !empty($news->coverImage)}
            <div class="news-image" style="background-image: url('{$news->coverImage}');"></div>
            {else}
            <div class="news-image" style="background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
                <i class="layui-icon layui-icon-picture" style="font-size: 24px; color: #ccc;"></i>
            </div>
            {/if}
            <div class="news-content">
                <a href="/article/{$news->id}" class="news-title">{$news->title}</a>
                <div class="news-meta">
                    <span><i class="layui-icon layui-icon-time"></i> {$news->publishTime|date='m-d H:i'}</span>
                    <span style="margin-left: 15px;"><i class="layui-icon layui-icon-read"></i> {$news->views ?? 0}</span>
                    {if !empty($news->categoryName)}
                    <span style="margin-left: 15px;"><i class="layui-icon layui-icon-tabs"></i> {$news->categoryName}</span>
                    {/if}
                </div>
                {if !empty($news->summary)}
                <div class="news-summary">{$news->summary}</div>
                {/if}
            </div>
        </li>
        {/volist}
    </ul>
    {else}
    <div class="ad-container">
        <i class="layui-icon layui-icon-file" style="font-size: 48px; color: #ddd;"></i>
        <p>暂无热门新闻</p>
        <p style="font-size: 12px; margin-top: 10px;">请在后台发布新闻文章</p>
    </div>
    {/if}
</div>
