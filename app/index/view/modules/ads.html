<!-- 广告位模块 -->
{if !empty($ads)}
    {volist name="ads" id="ad"}
    <div class="ad-item" style="{$ad->getSizeStyle()}">
        {switch name="ad->adType"}
            {case value="image"}
                {if !empty($ad->adUrl)}
                <a href="{$ad->adUrl}" target="{$ad->targetType}" onclick="trackAdClick({$ad->id})" title="{$ad->adTitle}">
                    <img src="{$ad->adImage}" alt="{$ad->adTitle}" style="width: 100%; height: 100%; object-fit: cover;">
                </a>
                {else}
                <img src="{$ad->adImage}" alt="{$ad->adTitle}" style="width: 100%; height: 100%; object-fit: cover;">
                {/if}
            {/case}
            {case value="text"}
                {if !empty($ad->adUrl)}
                <a href="{$ad->adUrl}" target="{$ad->targetType}" onclick="trackAdClick({$ad->id})" class="ad-text-link">
                    <div class="ad-text-content">
                        <h4>{$ad->adTitle}</h4>
                        {if !empty($ad->adContent)}
                        <p>{$ad->adContent}</p>
                        {/if}
                    </div>
                </a>
                {else}
                <div class="ad-text-content">
                    <h4>{$ad->adTitle}</h4>
                    {if !empty($ad->adContent)}
                    <p>{$ad->adContent}</p>
                    {/if}
                </div>
                {/if}
            {/case}
            {case value="html"}
                <div class="ad-html-content" onclick="trackAdClick({$ad->id})">
                    {$ad->adContent|raw}
                </div>
            {/case}
        {/switch}
    </div>
    {/volist}
{/if}

<style>
.ad-item {
    margin-bottom: 20px;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

.ad-item:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.ad-text-link {
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 20px;
    background: #fff;
    height: 100%;
    box-sizing: border-box;
}

.ad-text-content {
    padding: 20px;
    background: #fff;
    height: 100%;
    box-sizing: border-box;
}

.ad-text-content h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.ad-text-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

.ad-html-content {
    width: 100%;
    height: 100%;
}
</style>
