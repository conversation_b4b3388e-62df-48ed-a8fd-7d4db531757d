<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{if isset($e)}{$e->getMessage()}{else}{$error_title|default='错误页面'}{/if}</title>
    <meta name="keywords" content="错误页面,系统提示">
    <meta name="description" content="系统错误提示页面">
    <link rel="stylesheet" href="/static/layui/css/layui.css">
</head>
<body>
    <div class="error-page-wrapper">
        <div class="error-container">
            <div class="error-content">
                <!-- 错误图标 -->
                <div class="error-icon">
                    {if isset($e) && $e->getCode() == 404}
                        <i class="layui-icon layui-icon-404" style="font-size: 100px; color: #ff7875;"></i>
                    {elseif isset($e) && $e->getCode() == 403}
                        <i class="layui-icon layui-icon-close" style="font-size: 100px; color: #ff7875;"></i>
                    {elseif isset($e) && $e->getCode() == 500}
                        <i class="layui-icon layui-icon-engine" style="font-size: 100px; color: #ff7875;"></i>
                    {elseif isset($error_code)}
                        {switch name="error_code"}
                            {case value="404"}
                                <i class="layui-icon layui-icon-404" style="font-size: 100px; color: #ff7875;"></i>
                            {/case}
                            {case value="403"}
                                <i class="layui-icon layui-icon-close" style="font-size: 100px; color: #ff7875;"></i>
                            {/case}
                            {case value="500"}
                                <i class="layui-icon layui-icon-engine" style="font-size: 100px; color: #ff7875;"></i>
                            {/case}
                            {default /}
                                <i class="layui-icon layui-icon-face-cry" style="font-size: 100px; color: #ff7875;"></i>
                        {/switch}
                    {else}
                        <i class="layui-icon layui-icon-face-cry" style="font-size: 100px; color: #ff7875;"></i>
                    {/if}
                </div>

                <!-- 错误信息 -->
                <div class="error-info">
                    <h1 class="error-code">{if isset($e)}{$e->getCode()}{else}{$error_code|default='500'}{/if}</h1>
                    <h2 class="error-title">{if isset($e)}{$e->getMessage()}{else}{$error_title|default='系统错误'}{/if}</h2>
                    <p class="error-message">{if isset($e)}系统遇到了一些问题，请稍后再试{else}{$error_message|default='抱歉，系统遇到了一些问题，请稍后再试'}{/if}</p>

                    <!-- 根据错误类型显示不同的提示 -->
                    {if isset($e)}
                        {if $e->getCode() == 404}
                            <p class="error-tips">请检查网址是否正确，或者尝试以下操作</p>
                        {elseif $e->getCode() == 403}
                            <p class="error-tips">请确认您有足够的权限，或联系管理员获取访问权限</p>
                        {elseif $e->getCode() == 500}
                            <p class="error-tips">我们的技术团队已经收到通知，正在紧急处理中...</p>
                        {/if}
                    {elseif isset($error_code)}
                        {switch name="error_code"}
                            {case value="404"}
                                <p class="error-tips">请检查网址是否正确，或者尝试以下操作</p>
                            {/case}
                            {case value="403"}
                                <p class="error-tips">请确认您有足够的权限，或联系管理员获取访问权限</p>
                            {/case}
                            {case value="500"}
                                <p class="error-tips">我们的技术团队已经收到通知，正在紧急处理中...</p>
                            {/case}
                        {/switch}
                    {/if}
                </div>

                <!-- 操作按钮 -->
                <div class="error-actions">
                    <a href="/" class="error-btn error-btn-primary">
                        <i class="layui-icon layui-icon-home"></i> 返回首页
                    </a>
                    <a href="javascript:history.back();" class="error-btn error-btn-normal">
                        <i class="layui-icon layui-icon-return"></i> 返回上页
                    </a>
                    {if isset($error_code) && $error_code == '500'}
                    <a href="javascript:location.reload();" class="error-btn error-btn-warm">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新页面
                    </a>
                    {/if}
                </div>

                <!-- 搜索框（仅404页面显示） -->
                {if isset($error_code) && $error_code == '404'}
                <div class="error-search">
                    <h3>搜索您需要的内容：</h3>
                    <form action="/search" method="get" class="search-form">
                        <div class="search-input-group">
                            <input type="text" name="keyword" placeholder="输入关键词搜索..." class="search-input">
                            <button type="submit" class="search-btn">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                {/if}
            </div>
        </div>
    </div>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .error-page-wrapper {
            width: 100%;
            max-width: 800px;
            padding: 20px;
        }

        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
        }

        .error-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .error-icon {
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .error-code {
            font-size: 80px;
            font-weight: bold;
            color: #ff7875;
            margin: 0 0 15px 0;
            line-height: 1;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            font-size: 32px;
            color: #333;
            margin: 0 0 15px 0;
            font-weight: 600;
        }

        .error-message {
            font-size: 18px;
            color: #666;
            margin: 0 0 10px 0;
            line-height: 1.6;
        }

        .error-tips {
            font-size: 14px;
            color: #999;
            margin: 0 0 40px 0;
            font-style: italic;
        }

        .error-actions {
            margin-bottom: 40px;
        }

        .error-btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px 12px 0;
            border-radius: 8px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .error-btn-primary {
            background: #1890ff;
            color: #fff;
        }

        .error-btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .error-btn-normal {
            background: #52c41a;
            color: #fff;
        }

        .error-btn-normal:hover {
            background: #73d13d;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .error-btn-warm {
            background: #fa8c16;
            color: #fff;
        }

        .error-btn-warm:hover {
            background: #ffa940;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);
        }

        .error-search {
            margin: 30px 0;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .error-search h3 {
            font-size: 18px;
            color: #333;
            margin: 0 0 15px 0;
        }

        .search-input-group {
            display: flex;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: none;
            font-size: 14px;
            outline: none;
        }

        .search-btn {
            background: #1890ff;
            color: #fff;
            border: none;
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .search-btn:hover {
            background: #40a9ff;
        }

        .error-countdown {
            background: rgba(255, 247, 230, 0.8);
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 15px;
            margin: 30px 0;
            color: #d48806;
        }

        .error-countdown #countdown {
            font-weight: bold;
            color: #ff7875;
        }

        .countdown-cancel {
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 10px;
            transition: all 0.3s;
        }

        .countdown-cancel:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        .error-help {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 12px;
            padding: 25px;
            text-align: left;
            margin-top: 30px;
        }

        .error-help h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
            font-weight: 600;
        }

        .help-list {
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }

        .help-list li {
            margin-bottom: 8px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .contact-info {
            border-top: 1px solid #e8e8e8;
            padding-top: 15px;
            text-align: center;
        }

        .contact-info p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 10px;
            }

            .error-code {
                font-size: 60px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-message {
                font-size: 16px;
            }

            .error-btn {
                display: block;
                width: 100%;
                margin: 0 0 12px 0;
                text-align: center;
            }

            .search-input-group {
                flex-direction: column;
            }

            .search-btn {
                border-radius: 0 0 8px 8px;
            }

            .search-input {
                border-radius: 8px 8px 0 0;
            }
        }

        /* 动画效果 */
        .error-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <!-- 设置调试信息全局变量 -->
    {if isset($e) && app()->isDebug()}
    <script>
        window.errorDebugInfo = {
            file: "{$e->getFile()}",
            line: {$e->getLine()},
            trace: "{$e->getTraceAsString()|raw}",
            request_url: "{:request()->url(true)}",
            request_method: "{:request()->method()}"
        };
    </script>
    {elseif isset($debug_info) && $debug_info}
    <script>
        window.errorDebugInfo = {$debug_info|json_encode|raw};
    </script>
    {else}
    <script>
        window.errorDebugInfo = null;
    </script>
    {/if}

    <script>
        // 输出详细错误信息到浏览器控制台
        function outputErrorToConsole() {
            const errorCode = '{$error_code|default="未知"}';
            const errorTitle = '{$error_title|default="未知错误"}';
            const errorMessage = '{$error_message|default="无详细信息"}';

            console.group('🚨 系统错误详情');
            console.error('错误代码:', errorCode);
            console.error('错误标题:', errorTitle);
            console.error('错误信息:', errorMessage);
            console.error('发生时间:', new Date().toLocaleString());
            console.error('页面URL:', window.location.href);
            console.error('用户代理:', navigator.userAgent);

            // 调试信息（仅在调试模式下显示）
            const debugInfo = window.errorDebugInfo;
            if (debugInfo) {
                console.group('🔍 调试信息');
                console.error('错误文件:', debugInfo.file + ' 第 ' + debugInfo.line + ' 行');
                console.error('请求URL:', debugInfo.request_url);
                console.error('请求方法:', debugInfo.request_method);
                console.group('📋 堆栈跟踪');
                console.log(debugInfo.trace);
                console.groupEnd();
                console.groupEnd();
            }

            console.groupEnd();

            // 输出样式化的错误信息
            console.log('%c💥 发生了一个错误！', 'color: #ff4757; font-size: 16px; font-weight: bold;');
            console.log('%c请查看上方的错误详情进行调试', 'color: #747d8c; font-size: 12px;');

            if (debugInfo) {
                console.log('%c🔧 调试模式已开启，详细信息已输出到控制台', 'color: #5352ed; font-size: 12px;');
            } else {
                console.log('%c🔒 生产模式，详细调试信息已隐藏', 'color: #747d8c; font-size: 12px;');
            }
        }
    </script>
</body>
</html>
