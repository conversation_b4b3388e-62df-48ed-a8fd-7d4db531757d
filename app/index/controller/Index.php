<?php

namespace app\index\controller;

use app\common\bean\ArticleBean;
use app\common\repository\ArticleCategoryRepository;
use app\common\repository\ArticleRepository;
use app\common\service\ArticleService;
use app\common\service\ArticleCategoryService;
use think\App;
use think\Exception;
use think\exception\HttpException;
use think\facade\View;
use think\facade\Request;

class Index extends Base
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }

    /**
     * 首页
     */
    public function index()
    {
        try {
            // 获取各类新闻数据（这部分不缓存，因为需要实时更新）
            $articleRepository = new ArticleRepository();
            $bannerResult = $articleRepository->findWithPagination(['status' => 'published'], 1, 5);
            $hotResult = $articleRepository->findWithPagination(['status' => 'published'], 1, 5); // 限制为5条
            $latestResult = $articleRepository->findWithPagination(['status' => 'published'], 1, 5); // 限制为5条

            $newsData = [
                'banner' => $bannerResult['list'],
                'hot' => $hotResult['list'],
                'latest' => $latestResult['list']
            ];

            // 获取首页广告位数据
            $indexAds = [
                'index-1' => $this->getPortalAds('index-1'),
                'index-2' => $this->getPortalAds('index-2'),
                'index-3' => $this->getPortalAds('index-3'),
            ];

            // 传递数据到视图（公共数据已在BaseController中处理）
            View::assign([
                'title' => '首页 - 新闻门户',
                'currentCategoryId' => 0, // 首页没有当前分类
                'newsData' => $newsData,
                'indexAds' => $indexAds,
                'seoConfigs' => $this->getSiteConfigs() // 兼容模板中的seoConfigs变量
            ]);

            return View::fetch();
        } catch (Exception $e) {
            return View::fetch('error/index', ['e' => $e]);
        }
    }

    /**
     * 文章详情页
     * 
     * @param int $id 文章ID
     */
    public function article($id)
    {
        try {
            // 获取文章详情
            $article = (new ArticleService())->getById((int)$id);

            if (!$article || $article->status !== ArticleBean::STATUS_PUBLISHED) {
                throw new HttpException(404, '文章不存在');
            }

            // 更新阅读量和获取相关文章
            $articleRepository = new ArticleRepository();
            $articleRepository->incrementViewCount($id);

            // 获取相关文章
            $relatedArticles = $articleRepository->getRelatedArticles($article->categoryId, $article->id);

            // 获取分类列表
            $categoryRepository = new ArticleCategoryRepository();
            $categories = $categoryRepository->findAll(true);

            // 获取树形菜单结构
            $menuTree = (new ArticleCategoryService())->getCachedMenuTree(true);

            // 获取文章所属分类
            $articleCategory = $categoryRepository->findById($article->categoryId);
            $currentCategoryId = $this->getTopLevelCategoryId($articleCategory);

            // 传递数据到视图
            View::assign([
                'title' => $article->title . ' - 文章详情',
                'article' => $article,
                'relatedArticles' => $relatedArticles,
                'categories' => $categories,
                'menuTree' => $menuTree,
                'currentCategoryId' => $currentCategoryId,
                'customFields' => [], // 添加空的自定义字段数组
                'seoConfigs' => [
                    'site_keywords' => $article->title . ',文章,新闻',
                    'site_description' => $article->summary ?: mb_substr(strip_tags($article->content), 0, 150)
                ]
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (Exception $e) {
            return View::fetch('error/index', ['e' => $e]);
        }
    }

    /**
     * 分类文章列表页
     * 
     * @param int $id 分类ID
     */
    public function category($id)
    {
        try {
            // 获取分类信息
            $categoryRepository = new ArticleCategoryRepository();
            $category = $categoryRepository->findById((int)$id);

            if (!$category || !$category->isShow) {
                throw new HttpException(404, '分类不存在');
            }

            // 获取分页参数
            $page = (int)Request::param('page', 1);
            $limit = 10;

            // 获取分类下的文章
            $where = [
                'category_id' => $id,
                'status' => ArticleBean::STATUS_PUBLISHED
            ];
            $result = (new ArticleRepository())->findPublished($page, $limit, $where);

            // 获取分类列表
            $categories = $categoryRepository->findAll(true);

            // 获取树形菜单结构
            $menuTree = (new ArticleCategoryService())->getCachedMenuTree(true);

            // 获取当前分类的顶级分类ID（用于菜单高亮）
            $currentCategoryId = $this->getTopLevelCategoryId($category);

            // 传递数据到视图
            View::assign([
                'title' => $category->name . ' - 分类页面',
                'category' => $category,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories,
                'menuTree' => $menuTree,
                'currentCategoryId' => $currentCategoryId,
                'seoConfigs' => [
                    'site_keywords' => $category->name . ',文章,新闻',
                    'site_description' => $category->description ?: $category->name . '相关文章列表'
                ]
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (Exception $e) {
            return View::fetch('error/index', ['e' => $e]);
        }
    }

    /**
     * 搜索页面
     */
    public function search()
    {
        try {
            // 获取搜索关键词
            $keyword = Request::param('keyword', '');

            // 获取分页参数
            $page = (int)Request::param('page', 1);
            $limit = 10;
            
            // 如果关键词为空，重定向到首页
            if (empty($keyword)) {
                return redirect('/');
            }
            
            // 搜索文章
            $where = ['title' => $keyword];
            $result = (new ArticleRepository())->findPublished($page, $limit, $where);

            // 获取分类列表
            $categories = (new ArticleCategoryRepository())->findAll(true);

            // 获取树形菜单结构
            $menuTree = (new ArticleCategoryService())->getCachedMenuTree(true);

            // 传递数据到视图
            View::assign([
                'title' => '搜索: ' . $keyword . ' - 搜索结果',
                'keyword' => $keyword,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories,
                'menuTree' => $menuTree,
                'currentCategoryId' => 0, // 搜索页面没有当前分类
                'seoConfigs' => [
                    'site_keywords' => $keyword . ',搜索,文章,新闻',
                    'site_description' => '搜索"' . $keyword . '"的结果'
                ]
            ]);
            
            return View::fetch();
        } catch (Exception $e) {
            return View::fetch('error/index', ['e' => $e]);
        }
    }

    /**
     * 获取顶级分类ID
     */
    private function getTopLevelCategoryId($category): int
    {
        if (!$category) {
            return 0;
        }

        // 如果是顶级分类，直接返回ID
        if ($category->isTopLevel()) {
            return $category->id;
        }

        // 如果有路径信息，获取第一个路径ID（顶级分类）
        $pathArray = $category->getPathArray();
        if (!empty($pathArray)) {
            return $pathArray[0];
        }

        return 0;
    }
}
