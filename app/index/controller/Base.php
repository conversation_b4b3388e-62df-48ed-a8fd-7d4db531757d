<?php

namespace app\index\controller;

use app\BaseController;
use app\common\repository\ArticleCategoryRepository;
use app\common\repository\PortalConfigRepository;
use app\common\repository\PortalModuleRepository;
use app\common\repository\PortalBannerRepository;
use app\common\repository\PortalLinkRepository;
use app\common\repository\PortalAdRepository;
use app\common\service\ArticleCategoryService;
use think\App;
use think\facade\Cache;
use think\facade\View;

/**
 * 前台基础控制器 - 处理公共数据和缓存
 */
class Base extends BaseController
{
    // 缓存键名常量
    const CACHE_SITE_CONFIGS = 'site_configs';
    const CACHE_CATEGORIES = 'categories_tree';
    const CACHE_PORTAL_MODULES = 'portal_modules';
    const CACHE_PORTAL_BANNERS = 'portal_banners';
    const CACHE_PORTAL_LINKS = 'portal_links';
    const CACHE_PORTAL_ADS = 'portal_ads_';

    // 缓存过期时间（秒）
    const CACHE_EXPIRE = 3600; // 1小时

    public function __construct(App $app)
    {
        parent::__construct($app);

        // 初始化公共数据
        $this->initCommonData();
    }

    /**
     * 初始化公共数据
     */
    protected function initCommonData(): void
    {
        try {
            // 获取网站基础配置
            $siteConfigs = $this->getSiteConfigs();

            // 获取分类菜单树
            $menuTree = $this->getMenuTree();

            // 获取分类列表
            $categories = $this->getCategories();

            // 获取启用的门户模块
            $modules = $this->getPortalModules();

            // 获取轮播Banner
            $banners = $this->getPortalBanners();

            // 获取底部链接
            $links = $this->getPortalLinks();

            // 传递公共数据到视图
            View::assign([
                'siteConfigs' => $siteConfigs,
                'basicConfigs' => $siteConfigs, // 兼容旧模板
                'menuTree' => $menuTree,
                'categories' => $categories,
                'modules' => $modules,
                'portalBanners' => $banners,
                'portalLinks' => $links,
            ]);

        } catch (\Exception $e) {
            // 记录错误但不中断页面加载
            trace('初始化公共数据失败: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 获取网站配置（带缓存）
     */
    protected function getSiteConfigs(): array
    {
        return Cache::remember(self::CACHE_SITE_CONFIGS, function() {
            $repository = new PortalConfigRepository();
            return $repository->getGroupValues('site');
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取分类菜单树（带缓存）
     */
    protected function getMenuTree(): array
    {
        return Cache::remember(self::CACHE_CATEGORIES, function() {
            $service = new ArticleCategoryService();
            return $service->getCachedMenuTree(true);
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取分类列表（带缓存）
     */
    protected function getCategories(): array
    {
        return Cache::remember('categories_list', function() {
            $repository = new ArticleCategoryRepository();
            return $repository->findAll(true);
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取门户模块（带缓存）
     */
    protected function getPortalModules(): array
    {
        return Cache::remember(self::CACHE_PORTAL_MODULES, function() {
            $repository = new PortalModuleRepository();
            return $repository->findEnabled();
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取轮播Banner（带缓存）
     */
    protected function getPortalBanners(): array
    {
        return Cache::remember(self::CACHE_PORTAL_BANNERS, function() {
            $repository = new PortalBannerRepository();
            return $repository->findDisplayable(5); // 最多5个
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取底部链接（带缓存）
     */
    protected function getPortalLinks(): array
    {
        return Cache::remember(self::CACHE_PORTAL_LINKS, function() {
            $repository = new PortalLinkRepository();
            return $repository->findEnabledByCategory();
        }, self::CACHE_EXPIRE);
    }

    /**
     * 获取指定位置的广告（带缓存）
     */
    protected function getPortalAds(string $position): array
    {
        $cacheKey = self::CACHE_PORTAL_ADS . $position;
        return Cache::remember($cacheKey, function() use ($position) {
            $repository = new PortalAdRepository();
            return $repository->findDisplayableByPosition($position);
        }, self::CACHE_EXPIRE);
    }

    /**
     * 清除网站配置缓存
     */
    public static function clearSiteConfigsCache(): void
    {
        Cache::delete(self::CACHE_SITE_CONFIGS);
    }

    /**
     * 清除分类相关缓存
     */
    public static function clearCategoriesCache(): void
    {
        Cache::delete(self::CACHE_CATEGORIES);
        Cache::delete('categories_list');
    }

    /**
     * 清除门户模块缓存
     */
    public static function clearPortalModulesCache(): void
    {
        Cache::delete(self::CACHE_PORTAL_MODULES);
    }

    /**
     * 清除轮播Banner缓存
     */
    public static function clearPortalBannersCache(): void
    {
        Cache::delete(self::CACHE_PORTAL_BANNERS);
    }

    /**
     * 清除底部链接缓存
     */
    public static function clearPortalLinksCache(): void
    {
        Cache::delete(self::CACHE_PORTAL_LINKS);
    }

    /**
     * 清除指定位置的广告缓存
     */
    public static function clearPortalAdsCache(string $position = ''): void
    {
        if ($position) {
            Cache::delete(self::CACHE_PORTAL_ADS . $position);
        } else {
            // 清除所有广告位缓存
            $positions = ['index-1', 'index-2', 'index-3', 'sidebar', 'header', 'footer'];
            foreach ($positions as $pos) {
                Cache::delete(self::CACHE_PORTAL_ADS . $pos);
            }
        }
    }

    /**
     * 清除所有缓存
     */
    public static function clearAllCache(): void
    {
        self::clearSiteConfigsCache();
        self::clearCategoriesCache();
        self::clearPortalModulesCache();
        self::clearPortalBannersCache();
        self::clearPortalLinksCache();
        self::clearPortalAdsCache();

        // 清除其他可能的缓存
        Cache::clear();
    }
}