<?php

namespace app\index\controller;

use think\facade\View;
use think\Exception;

class ErrorTest extends Base
{
    /**
     * 测试新的错误页面（使用$e参数）
     */
    public function testNewError()
    {
        try {
            // 模拟一个错误
            throw new Exception('这是一个测试错误', 500);
        } catch (Exception $e) {
            // 使用新的错误页面格式，只传入$e参数
            View::assign(['e' => $e]);
            return View::fetch('error/index');
        }
    }
    
    /**
     * 测试404错误
     */
    public function test404()
    {
        $e = new Exception('页面不存在', 404);
        View::assign(['e' => $e]);
        return View::fetch('error/index');
    }
    
    /**
     * 测试403错误
     */
    public function test403()
    {
        $e = new Exception('访问被拒绝', 403);
        View::assign(['e' => $e]);
        return View::fetch('error/index');
    }
}
