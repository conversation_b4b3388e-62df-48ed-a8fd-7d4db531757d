<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\bean\AdminUserBean;
use app\common\repository\AdminUserRepository;
use app\common\repository\AdminLogRepository;
use think\facade\Session;
use think\exception\ValidateException;

/**
 * 管理员认证服务
 */
class AdminAuthService
{
    const SESSION_KEY = 'admin_user';

    public function __construct()
    {
    }
    
    /**
     * 登录
     */
    public function login(string $username, string $password, string $ip = ''): array
    {
        // 查找用户
        $userRepository = new AdminUserRepository();
        $user = $userRepository->findByUsername($username);
        if (!$user) {
            throw new ValidateException('用户名或密码错误');
        }

        // 检查状态
        if ($user->status != AdminUserBean::STATUS_ENABLED) {
            throw new ValidateException('账号已被禁用');
        }

        // 验证密码
        if (!$user->checkPassword($password)) {
            throw new ValidateException('用户名或密码错误');
        }

        // 更新登录信息
        $user->updateLastLogin($ip ?: request()->ip());
        $userRepository->update($user);

        // 存储到Session
        Session::set(self::SESSION_KEY, [
            'id' => $user->id,
            'username' => $user->username,
            'real_name' => $user->realName,
            'email' => $user->email,
            'role' => $user->role,
            'avatar' => $user->avatar,
            'login_time' => time(),
        ]);

        // 记录登录日志
        (new AdminLogRepository())->record(
            $user->id,
            $user->username,
            'login',
            '管理员登录',
            $ip ?: request()->ip()
        );
        
        return [
            'id' => $user->id,
            'username' => $user->username,
            'real_name' => $user->realName,
            'email' => $user->email,
            'role' => $user->role,
            'role_text' => $user->getRoleText(),
        ];
    }
    
    /**
     * 登出
     */
    public function logout(): bool
    {
        $user = $this->getUser();
        if ($user) {
            // 记录登出日志
            (new AdminLogRepository())->record(
                $user['id'],
                $user['username'],
                'logout',
                '管理员登出'
            );
        }
        
        // 清除Session
        Session::delete(self::SESSION_KEY);
        
        return true;
    }
    
    /**
     * 检查是否已登录
     */
    public function isLogin(): bool
    {
        return Session::has(self::SESSION_KEY);
    }
    
    /**
     * 获取当前登录用户信息
     */
    public function getUser(): ?array
    {
        return Session::get(self::SESSION_KEY);
    }
    
    /**
     * 获取当前登录用户ID
     */
    public function getUserId(): int
    {
        $user = $this->getUser();
        return $user ? $user['id'] : 0;
    }
    
    /**
     * 获取当前登录用户角色
     */
    public function getUserRole(): string
    {
        $user = $this->getUser();
        return $user ? $user['role'] : '';
    }
    
    /**
     * 检查当前用户是否有指定角色
     */
    public function hasRole(string $role): bool
    {
        return $this->getUserRole() === $role;
    }
    
    /**
     * 检查当前用户是否有指定角色之一
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->getUserRole(), $roles);
    }
    
    /**
     * 检查是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole(AdminUserBean::ROLE_SUPER_ADMIN);
    }
    
    /**
     * 刷新用户信息
     */
    public function refreshUser(): bool
    {
        $userId = $this->getUserId();
        if (!$userId) {
            return false;
        }
        
        $user = (new AdminUserRepository())->findById($userId);
        if (!$user || $user->status != AdminUserBean::STATUS_ENABLED) {
            $this->logout();
            return false;
        }
        
        // 更新Session中的用户信息
        Session::set(self::SESSION_KEY, [
            'id' => $user->id,
            'username' => $user->username,
            'real_name' => $user->realName,
            'email' => $user->email,
            'role' => $user->role,
            'avatar' => $user->avatar,
            'login_time' => Session::get(self::SESSION_KEY)['login_time'] ?? time(),
        ]);
        
        return true;
    }
}
