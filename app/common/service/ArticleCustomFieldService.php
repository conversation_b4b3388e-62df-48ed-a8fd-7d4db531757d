<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\bean\ArticleCustomFieldBean;
use app\common\repository\ArticleCustomFieldRepository;
use app\common\repository\ArticleCustomFieldValueRepository;
use think\exception\ValidateException;

/**
 * 文章自定义字段Service
 */
class ArticleCustomFieldService
{
    public function __construct()
    {
    }

    /**
     * 获取字段列表
     */
    public function getList(array $where = [], int $page = 1, int $limit = 15): array
    {
        return (new ArticleCustomFieldRepository())->findWithPagination($where, $page, $limit);
    }

    /**
     * 获取所有启用的字段
     */
    public function getActiveFields(): array
    {
        return (new ArticleCustomFieldRepository())->findActive();
    }

    /**
     * 获取所有字段
     */
    public function getAllFields(): array
    {
        return (new ArticleCustomFieldRepository())->findAll();
    }

    /**
     * 根据ID获取字段详情
     */
    public function getById(int $id): ?ArticleCustomFieldBean
    {
        return (new ArticleCustomFieldRepository())->findById($id);
    }

    /**
     * 根据字段键名获取字段
     */
    public function getByFieldKey(string $fieldKey): ?ArticleCustomFieldBean
    {
        return (new ArticleCustomFieldRepository())->findByFieldKey($fieldKey);
    }

    /**
     * 创建字段
     */
    public function create(array $data): ArticleCustomFieldBean
    {
        // 验证数据
        $this->validateFieldData($data);
        
        // 创建字段Bean
        $field = new ArticleCustomFieldBean();
        $this->fillFieldBean($field, $data);
        
        // 验证字段键名唯一性
        $fieldRepository = new ArticleCustomFieldRepository();
        if (!$fieldRepository->isFieldKeyUnique($field->fieldKey)) {
            throw new ValidateException('字段键名已存在');
        }

        // 保存字段
        $fieldId = $fieldRepository->create($field);
        
        return $this->getById($fieldId);
    }

    /**
     * 更新字段
     */
    public function update(int $id, array $data): ArticleCustomFieldBean
    {
        $fieldRepository = new ArticleCustomFieldRepository();
        $field = $fieldRepository->findById($id);
        if (!$field) {
            throw new ValidateException('字段不存在');
        }

        // 验证数据
        $this->validateFieldData($data, $id);

        // 更新字段Bean
        $this->fillFieldBean($field, $data);

        // 验证字段键名唯一性
        if (isset($data['field_key']) && !$fieldRepository->isFieldKeyUnique($field->fieldKey, $id)) {
            throw new ValidateException('字段键名已存在');
        }

        // 保存字段
        $fieldRepository->update($field);

        return $this->getById($id);
    }

    /**
     * 删除字段
     */
    public function delete(int $id): bool
    {
        $fieldRepository = new ArticleCustomFieldRepository();
        $field = $fieldRepository->findById($id);
        if (!$field) {
            throw new ValidateException('字段不存在');
        }

        // 检查是否有文章使用了该字段
        $valueRepository = new ArticleCustomFieldValueRepository();
        $usageStats = $valueRepository->getFieldUsageStats($id);
        if ($usageStats['total_usage'] > 0) {
            throw new ValidateException('该字段正在被使用，无法删除');
        }

        // 删除字段值（虽然上面检查了，但为了安全起见）
        $valueRepository->deleteByFieldId($id);

        // 删除字段
        return $fieldRepository->delete($id);
    }

    /**
     * 切换启用状态
     */
    public function toggleActive(int $id): bool
    {
        return (new ArticleCustomFieldRepository())->toggleActive($id);
    }

    /**
     * 批量更新排序
     */
    public function updateSort(array $sortData): bool
    {
        return (new ArticleCustomFieldRepository())->updateSort($sortData);
    }

    /**
     * 获取字段统计信息
     */
    public function getStatistics(): array
    {
        $fieldStats = (new ArticleCustomFieldRepository())->getStatistics();
        $valueStats = (new ArticleCustomFieldValueRepository())->getStatistics();

        return array_merge($fieldStats, $valueStats);
    }

    /**
     * 根据字段类型获取字段
     */
    public function getByType(string $fieldType, bool $onlyActive = true): array
    {
        return (new ArticleCustomFieldRepository())->findByType($fieldType, $onlyActive);
    }

    /**
     * 获取必填字段
     */
    public function getRequiredFields(bool $onlyActive = true): array
    {
        return (new ArticleCustomFieldRepository())->findRequired($onlyActive);
    }

    /**
     * 获取字段的使用统计
     */
    public function getFieldUsageStats(int $fieldId): array
    {
        return (new ArticleCustomFieldValueRepository())->getFieldUsageStats($fieldId);
    }

    /**
     * 验证字段数据
     */
    private function validateFieldData(array $data, int $excludeId = 0): void
    {
        // 必填字段验证
        if (empty($data['name'])) {
            throw new ValidateException('字段名称不能为空');
        }
        
        if (empty($data['field_key'])) {
            throw new ValidateException('字段键名不能为空');
        }
        
        if (empty($data['field_type'])) {
            throw new ValidateException('字段类型不能为空');
        }
        
        // 字段键名格式验证
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $data['field_key'])) {
            throw new ValidateException('字段键名格式不正确，必须以字母开头，只能包含字母、数字和下划线');
        }
        
        // 字段类型验证
        if (!in_array($data['field_type'], [
            ArticleCustomFieldBean::TYPE_TAG,
            ArticleCustomFieldBean::TYPE_TEXT,
            ArticleCustomFieldBean::TYPE_TEXTAREA
        ])) {
            throw new ValidateException('无效的字段类型');
        }
        
        // 验证选项配置
        if (isset($data['options']) && !empty($data['options'])) {
            if (is_string($data['options'])) {
                $decoded = json_decode($data['options'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new ValidateException('选项配置格式不正确');
                }
            } elseif (!is_array($data['options'])) {
                throw new ValidateException('选项配置必须是数组或JSON字符串');
            }
        }
        
        // 验证验证规则
        if (isset($data['validation_rules']) && !empty($data['validation_rules'])) {
            if (is_string($data['validation_rules'])) {
                $decoded = json_decode($data['validation_rules'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new ValidateException('验证规则格式不正确');
                }
            } elseif (!is_array($data['validation_rules'])) {
                throw new ValidateException('验证规则必须是数组或JSON字符串');
            }
        }
    }

    /**
     * 填充字段Bean
     */
    private function fillFieldBean(ArticleCustomFieldBean $field, array $data): void
    {
        $fields = [
            'name' => 'name',
            'field_key' => 'fieldKey',
            'field_type' => 'fieldType',
            'is_required' => 'isRequired',
            'default_value' => 'defaultValue',
            'sort_order' => 'sortOrder',
            'is_active' => 'isActive',
            'description' => 'description'
        ];
        
        foreach ($fields as $key => $property) {
            if (array_key_exists($key, $data)) {
                $field->$property = $data[$key];
            }
        }
        
        // 处理选项配置
        if (array_key_exists('options', $data)) {
            if (is_array($data['options'])) {
                $field->setOptionsArray($data['options']);
            } else {
                $field->options = $data['options'];
            }
        }
        
        // 处理验证规则
        if (array_key_exists('validation_rules', $data)) {
            if (is_array($data['validation_rules'])) {
                $field->setValidationRulesArray($data['validation_rules']);
            } else {
                $field->validationRules = $data['validation_rules'];
            }
        }
        
        // 设置默认值
        if (is_null($field->isRequired)) {
            $field->isRequired = ArticleCustomFieldBean::REQUIRED_NO;
        }
        if (is_null($field->isActive)) {
            $field->isActive = ArticleCustomFieldBean::ACTIVE_YES;
        }
        if (is_null($field->sortOrder)) {
            $field->sortOrder = 0;
        }
    }

    /**
     * 验证字段值
     */
    public function validateFieldValue(int $fieldId, $value): array
    {
        $field = $this->getById($fieldId);
        if (!$field) {
            return ['字段不存在'];
        }
        
        return $field->validateFieldValue($value);
    }

    /**
     * 获取字段的键值对映射
     */
    public function getFieldKeyMap(bool $onlyActive = true): array
    {
        return (new ArticleCustomFieldRepository())->getFieldKeyMap($onlyActive);
    }
}
