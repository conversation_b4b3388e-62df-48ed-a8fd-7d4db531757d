<?php

namespace app\common\service;

use app\common\repository\PortalConfigRepository;
use think\facade\Cache;
use think\exception\ValidateException;
use think\Exception;

/**
 * 门户配置服务类
 */
class PortalConfigService
{
    // 缓存前缀
    const CACHE_PREFIX = 'portal_config:';

    // 缓存过期时间（秒）
    const CACHE_EXPIRE = 3600;

    private PortalConfigRepository $repository;

    public function __construct()
    {
        $this->repository = new PortalConfigRepository();
    }
    
    /**
     * 获取配置列表（分页）
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $group 分组筛选
     * @param string $keyword 关键词搜索
     * @return array
     */
    public function getConfigList(int $page = 1, int $limit = 20, string $group = '', string $keyword = ''): array
    {
        $where = [];

        // 分组筛选
        if (!empty($group)) {
            $where['group_name'] = $group;
        }

        // 关键词搜索
        if (!empty($keyword)) {
            $where['config_key'] = $keyword; // Repository会处理模糊搜索
        }

        return $this->repository->findWithPagination($where, $page, $limit);
    }
    
    /**
     * 获取配置分组列表
     *
     * @return array
     */
    public function getConfigGroups(): array
    {
        return $this->repository->getGroups();
    }
    
    /**
     * 根据ID获取配置
     *
     * @param int $id 配置ID
     * @return mixed
     */
    public function getConfigById(int $id)
    {
        return $this->repository->findById($id);
    }
    
    /**
     * 根据键名获取配置值
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @param bool $useCache 是否使用缓存
     * @return mixed
     */
    public function getConfigValue(string $key, $default = null, bool $useCache = true)
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . $key;
            $value = Cache::get($cacheKey);
            
            if ($value !== false) {
                return $value;
            }
        }
        
        $value = $this->repository->getValue($key, $default);
        
        if ($useCache) {
            Cache::set($cacheKey, $value, self::CACHE_EXPIRE);
        }
        
        return $value;
    }
    
    /**
     * 根据分组获取配置
     *
     * @param string $group 分组名称
     * @param bool $useCache 是否使用缓存
     * @return array
     */
    public function getConfigByGroup(string $group, bool $useCache = true): array
    {
        if ($useCache) {
            $cacheKey = self::CACHE_PREFIX . 'group:' . $group;
            $configs = Cache::get($cacheKey);

            if ($configs !== false && is_array($configs)) {
                return $configs;
            }
        }

        $configs = $this->repository->getGroupValues($group);

        if ($useCache) {
            Cache::set($cacheKey, $configs, self::CACHE_EXPIRE);
        }

        return $configs;
    }
    
    /**
     * 创建配置
     *
     * @param array $data 配置数据
     * @return mixed
     * @throws ValidateException
     */
    public function createConfig(array $data)
    {
        // 数据验证
        $this->validateConfigData($data);

        // 检查键名是否已存在
        if ($this->repository->existsByKey($data['config_key'])) {
            throw new ValidateException('配置键名已存在');
        }

        // 创建Bean对象
        $configBean = new \app\common\bean\PortalConfigBean();
        $configBean->configKey = $data['config_key'];
        $configBean->configValue = $data['config_value'] ?? '';
        $configBean->configType = $data['config_type'] ?? 'string';
        $configBean->groupName = $data['group_name'] ?? 'default';
        $configBean->description = $data['description'] ?? '';
        $configBean->sortOrder = $data['sort_order'] ?? 0;
        $configBean->isSystem = $data['is_system'] ?? 0;

        $config = $this->repository->create($configBean);

        // 清除相关缓存
        $this->clearConfigCache($data['config_key'], $data['group_name']);

        return $config;
    }
    
    /**
     * 更新配置
     *
     * @param int $id 配置ID
     * @param array $data 配置数据
     * @return PortalConfig
     * @throws ValidateException
     */
    public function updateConfig(int $id, array $data)
    {
        $config = $this->repository->findById($id);
        if (!$config) {
            throw new ValidateException('配置不存在');
        }

        // 系统配置的键名不允许修改
        if ($config->isSystemConfig() && isset($data['config_key']) && $data['config_key'] !== $config->configKey) {
            throw new ValidateException('系统配置的键名不允许修改');
        }

        // 验证数据
        $this->validateConfigData($data, $id);

        // 检查键名是否已存在（排除当前记录）
        if (isset($data['config_key']) && $data['config_key'] !== $config->configKey) {
            if ($this->repository->existsByKey($data['config_key'], $id)) {
                throw new ValidateException('配置键名已存在');
            }
        }

        $oldKey = $config->configKey;
        $oldGroup = $config->groupName;

        // 更新Bean属性
        if (isset($data['config_key'])) $config->configKey = $data['config_key'];
        if (isset($data['config_value'])) $config->configValue = $data['config_value'];
        if (isset($data['config_type'])) $config->configType = $data['config_type'];
        if (isset($data['group_name'])) $config->groupName = $data['group_name'];
        if (isset($data['description'])) $config->description = $data['description'];
        if (isset($data['sort_order'])) $config->sortOrder = $data['sort_order'];
        if (isset($data['is_system'])) $config->isSystem = $data['is_system'];

        $updatedConfig = $this->repository->update($config);

        // 清除相关缓存
        $this->clearConfigCache($oldKey, $oldGroup);
        if (isset($data['config_key']) && $data['config_key'] !== $oldKey) {
            $this->clearConfigCache($data['config_key'], $data['group_name'] ?? $config->groupName);
        }

        return $updatedConfig;
    }
    
    /**
     * 删除配置
     *
     * @param int $id 配置ID
     * @return bool
     * @throws ValidateException
     */
    public function deleteConfig(int $id): bool
    {
        $config = $this->repository->findById($id);
        if (!$config) {
            throw new ValidateException('配置不存在');
        }

        if ($config->isSystemConfig()) {
            throw new ValidateException('系统配置不允许删除');
        }

        $key = $config->configKey;
        $group = $config->groupName;

        $result = $this->repository->delete($id);

        if ($result) {
            // 清除相关缓存
            $this->clearConfigCache($key, $group);
        }

        return $result;
    }
    
    /**
     * 批量设置配置
     *
     * @param array $configs 配置数组 [key => value, ...]
     * @param string $group 分组名称
     * @return bool
     */
    public function batchSetConfigs(array $configs, string $group = 'default'): bool
    {
        try {
            foreach ($configs as $key => $value) {
                $type = $this->guessConfigType($value);

                // 检查配置是否存在
                $existingConfig = $this->repository->findByKey($key);
                if ($existingConfig) {
                    // 更新现有配置
                    $existingConfig->configValue = (string)$value;
                    $existingConfig->configType = $type;
                    $this->repository->update($existingConfig);
                } else {
                    // 创建新配置
                    $configBean = new \app\common\bean\PortalConfigBean();
                    $configBean->configKey = $key;
                    $configBean->configValue = (string)$value;
                    $configBean->configType = $type;
                    $configBean->groupName = $group;
                    $configBean->description = '';
                    $configBean->sortOrder = 0;
                    $configBean->isSystem = 0;
                    $this->repository->create($configBean);
                }
            }

            // 清除分组缓存
            $this->clearGroupCache($group);

            return true;
        } catch (Exception $e) {
            throw new ValidateException('批量设置配置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 验证配置数据
     *
     * @param array $data 配置数据
     * @param int $excludeId 排除的ID
     * @throws ValidateException
     */
    protected function validateConfigData(array $data, int $excludeId = 0): void
    {
        // 必填字段验证
        if (empty($data['config_key'])) {
            throw new ValidateException('配置键名不能为空');
        }
        
        // 键名格式验证
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $data['config_key'])) {
            throw new ValidateException('配置键名格式不正确，只能包含字母、数字和下划线，且必须以字母开头');
        }
        
        // 配置类型验证
        if (isset($data['config_type']) && !in_array($data['config_type'], ['string', 'number', 'boolean', 'json'])) {
            throw new ValidateException('配置类型不正确');
        }
        
        // JSON格式验证
        if (isset($data['config_type']) && $data['config_type'] === 'json' && isset($data['config_value'])) {
            if (!is_array($data['config_value']) && json_decode($data['config_value']) === null) {
                throw new ValidateException('JSON格式配置值不正确');
            }
        }
    }
    
    /**
     * 猜测配置类型
     *
     * @param mixed $value 配置值
     * @return string
     */
    protected function guessConfigType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_numeric($value)) {
            return 'number';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }
    
    /**
     * 清除配置缓存
     *
     * @param string $key 配置键名
     * @param string $group 分组名称
     */
    protected function clearConfigCache(string $key, string $group): void
    {
        Cache::delete(self::CACHE_PREFIX . $key);
        Cache::delete(self::CACHE_PREFIX . 'group:' . $group);
    }
    
    /**
     * 清除分组缓存
     *
     * @param string $group 分组名称
     */
    protected function clearGroupCache(string $group): void
    {
        Cache::delete(self::CACHE_PREFIX . 'group:' . $group);
    }
    
    /**
     * 清除所有配置缓存
     */
    public function clearAllCache(): void
    {
        Cache::clear();
    }
}
