<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\PortalLinkBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 底部链接 Repository
 */
class PortalLinkRepository
{
    /**
     * 根据ID查找链接
     */
    public function findById(int $id): ?PortalLinkBean
    {
        $bean = new PortalLinkBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }
    
    /**
     * 获取启用的链接列表
     */
    public function findEnabled(): array
    {
        $bean = new PortalLinkBean();
        return EntityManager::create($bean)
            ->where('is_enabled', PortalLinkBean::ENABLED_YES)
            ->order('category_sort', 'ASC')
            ->order('sort_order', 'ASC')
            ->order('id', 'ASC')
            ->selectResult();
    }
    
    /**
     * 按分类获取启用的链接
     */
    public function findEnabledByCategory(): array
    {
        $links = $this->findEnabled();
        $grouped = [];
        
        foreach ($links as $link) {
            $grouped[$link->categoryName][] = $link;
        }
        
        return $grouped;
    }
    
    /**
     * 获取所有分类名称
     */
    public function getCategories(): array
    {
        $bean = new PortalLinkBean();
        $results = EntityManager::create($bean)
            ->field('category_name, category_sort')
            ->where('is_enabled', PortalLinkBean::ENABLED_YES)
            ->group('category_name')
            ->order('category_sort', 'ASC')
            ->selectResult();
        
        return array_column($results, 'category_name');
    }
    
    /**
     * 分页查询链接
     */
    public function findWithPagination(array $conditions = [], int $page = 1, int $limit = 20): array
    {
        $bean = new PortalLinkBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($conditions['category_name'])) {
            $query->where('category_name', $conditions['category_name']);
        }
        if (!empty($conditions['link_title'])) {
            $query->where('link_title', 'like', '%' . $conditions['link_title'] . '%');
        }
        if (isset($conditions['is_enabled'])) {
            $query->where('is_enabled', $conditions['is_enabled']);
        }
        
        // 获取总数
        $total = $query->count();
        
        // 分页查询
        $offset = ($page - 1) * $limit;
        $list = $query->order('category_sort', 'ASC')
            ->order('sort_order', 'ASC')
            ->order('id', 'DESC')
            ->limit($offset, $limit)
            ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 创建链接
     */
    public function create(PortalLinkBean $link): PortalLinkBean
    {
        // 验证数据
        $this->validateLink($link);
        
        // 设置默认值
        if (is_null($link->sortOrder)) {
            $link->sortOrder = $this->getNextSortOrder($link->categoryName);
        }
        if (is_null($link->categorySort)) {
            $link->categorySort = $this->getNextCategorySort();
        }
        if (is_null($link->isEnabled)) {
            $link->isEnabled = PortalLinkBean::ENABLED_YES;
        }
        
        $bean = new PortalLinkBean();
        $result = EntityManager::create($bean)
            ->insert([
                'category_name' => $link->categoryName,
                'link_title' => $link->linkTitle,
                'link_url' => $link->linkUrl,
                'description' => $link->description,
                'sort_order' => $link->sortOrder,
                'category_sort' => $link->categorySort,
                'is_enabled' => $link->isEnabled,
                'target_type' => $link->targetType,
                'is_nofollow' => $link->isNofollow,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($result);
        }
        
        throw new ValidateException('创建链接失败');
    }
    
    /**
     * 更新链接
     */
    public function update(PortalLinkBean $link): PortalLinkBean
    {
        if (!$link->id) {
            throw new ValidateException('链接ID不能为空');
        }
        
        // 验证数据
        $this->validateLink($link);
        
        $bean = new PortalLinkBean();
        $result = EntityManager::create($bean)
            ->where('id', $link->id)
            ->update([
                'category_name' => $link->categoryName,
                'link_title' => $link->linkTitle,
                'link_url' => $link->linkUrl,
                'description' => $link->description,
                'sort_order' => $link->sortOrder,
                'category_sort' => $link->categorySort,
                'is_enabled' => $link->isEnabled,
                'target_type' => $link->targetType,
                'is_nofollow' => $link->isNofollow,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($link->id);
        }
        
        throw new ValidateException('更新链接失败');
    }
    
    /**
     * 删除链接
     */
    public function delete(int $id): bool
    {
        $bean = new PortalLinkBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->delete() > 0;
    }
    
    /**
     * 更新点击次数
     */
    public function updateClickCount(int $id): bool
    {
        $bean = new PortalLinkBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->update(['click_count' => ['inc', 1]]) > 0;
    }
    
    /**
     * 获取分类下一个排序号
     */
    private function getNextSortOrder(string $categoryName): int
    {
        $bean = new PortalLinkBean();
        $maxSort = EntityManager::create($bean)
            ->where('category_name', $categoryName)
            ->max('sort_order');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 获取下一个分类排序号
     */
    private function getNextCategorySort(): int
    {
        $bean = new PortalLinkBean();
        $maxSort = EntityManager::create($bean)->max('category_sort');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 验证链接数据
     */
    private function validateLink(PortalLinkBean $link): void
    {
        if (empty($link->categoryName)) {
            throw new ValidateException('分类名称不能为空');
        }
        if (empty($link->linkTitle)) {
            throw new ValidateException('链接标题不能为空');
        }
        if (empty($link->linkUrl)) {
            throw new ValidateException('链接URL不能为空');
        }
    }
}
