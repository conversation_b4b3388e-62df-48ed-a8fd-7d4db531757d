<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\PortalBannerBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 轮播Banner Repository
 */
class PortalBannerRepository
{
    /**
     * 根据ID查找Banner
     */
    public function findById(int $id): ?PortalBannerBean
    {
        $bean = new PortalBannerBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }
    
    /**
     * 获取启用的Banner列表
     */
    public function findEnabled(int $limit = 0): array
    {
        $bean = new PortalBannerBean();
        $query = EntityManager::create($bean)
            ->where('is_enabled', PortalBannerBean::ENABLED_YES)
            ->order('sort_order', 'ASC')
            ->order('id', 'ASC');
        
        if ($limit > 0) {
            $query->limit($limit);
        }
        
        return $query->selectResult();
    }
    
    /**
     * 获取可显示的Banner列表（启用且在有效期内）
     */
    public function findDisplayable(int $limit = 0): array
    {
        $banners = $this->findEnabled($limit);
        
        return array_filter($banners, function($banner) {
            return $banner->canDisplay();
        });
    }
    
    /**
     * 分页查询Banner
     */
    public function findWithPagination(array $conditions = [], int $page = 1, int $limit = 20): array
    {
        $bean = new PortalBannerBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($conditions['title'])) {
            $query->where('title', 'like', '%' . $conditions['title'] . '%');
        }
        if (isset($conditions['is_enabled'])) {
            $query->where('is_enabled', $conditions['is_enabled']);
        }
        
        // 获取总数
        $total = $query->count();
        
        // 分页查询
        $offset = ($page - 1) * $limit;
        $list = $query->order('sort_order', 'ASC')
            ->order('id', 'DESC')
            ->limit($offset, $limit)
            ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 创建Banner
     */
    public function create(PortalBannerBean $banner): PortalBannerBean
    {
        // 验证数据
        $this->validateBanner($banner);
        
        // 设置默认值
        if (is_null($banner->sortOrder)) {
            $banner->sortOrder = $this->getNextSortOrder();
        }
        if (is_null($banner->isEnabled)) {
            $banner->isEnabled = PortalBannerBean::ENABLED_YES;
        }
        
        $bean = new PortalBannerBean();
        $result = EntityManager::create($bean)
            ->insert([
                'title' => $banner->title,
                'image_url' => $banner->imageUrl,
                'link_url' => $banner->linkUrl,
                'description' => $banner->description,
                'sort_order' => $banner->sortOrder,
                'is_enabled' => $banner->isEnabled,
                'target_type' => $banner->targetType,
                'start_time' => $banner->startTime,
                'end_time' => $banner->endTime,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($result);
        }
        
        throw new ValidateException('创建Banner失败');
    }
    
    /**
     * 更新Banner
     */
    public function update(PortalBannerBean $banner): PortalBannerBean
    {
        if (!$banner->id) {
            throw new ValidateException('Banner ID不能为空');
        }
        
        // 验证数据
        $this->validateBanner($banner);
        
        $bean = new PortalBannerBean();
        $result = EntityManager::create($bean)
            ->where('id', $banner->id)
            ->update([
                'title' => $banner->title,
                'image_url' => $banner->imageUrl,
                'link_url' => $banner->linkUrl,
                'description' => $banner->description,
                'sort_order' => $banner->sortOrder,
                'is_enabled' => $banner->isEnabled,
                'target_type' => $banner->targetType,
                'start_time' => $banner->startTime,
                'end_time' => $banner->endTime,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($banner->id);
        }
        
        throw new ValidateException('更新Banner失败');
    }
    
    /**
     * 删除Banner
     */
    public function delete(int $id): bool
    {
        $bean = new PortalBannerBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->delete() > 0;
    }
    
    /**
     * 更新点击次数
     */
    public function updateClickCount(int $id): bool
    {
        $bean = new PortalBannerBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->update(['click_count' => ['inc', 1]]) > 0;
    }
    
    /**
     * 获取下一个排序号
     */
    private function getNextSortOrder(): int
    {
        $bean = new PortalBannerBean();
        $maxSort = EntityManager::create($bean)->max('sort_order');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 验证Banner数据
     */
    private function validateBanner(PortalBannerBean $banner): void
    {
        if (empty($banner->title)) {
            throw new ValidateException('Banner标题不能为空');
        }
        if (empty($banner->imageUrl)) {
            throw new ValidateException('Banner图片不能为空');
        }
        if (empty($banner->linkUrl)) {
            throw new ValidateException('Banner链接不能为空');
        }
    }
}
