<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\PortalAdBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 广告位 Repository
 */
class PortalAdRepository
{
    /**
     * 根据ID查找广告
     */
    public function findById(int $id): ?PortalAdBean
    {
        $bean = new PortalAdBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }
    
    /**
     * 根据广告位标识查找广告
     */
    public function findByPosition(string $position): array
    {
        $bean = new PortalAdBean();
        return EntityManager::create($bean)
            ->where('ad_position', $position)
            ->where('is_enabled', PortalAdBean::ENABLED_YES)
            ->order('sort_order', 'ASC')
            ->order('id', 'ASC')
            ->selectResult();
    }
    
    /**
     * 获取可显示的广告（启用且在有效期内）
     */
    public function findDisplayableByPosition(string $position): array
    {
        $ads = $this->findByPosition($position);
        
        return array_filter($ads, function($ad) {
            return $ad->canDisplay();
        });
    }
    
    /**
     * 获取启用的广告列表
     */
    public function findEnabled(): array
    {
        $bean = new PortalAdBean();
        return EntityManager::create($bean)
            ->where('is_enabled', PortalAdBean::ENABLED_YES)
            ->order('ad_position', 'ASC')
            ->order('sort_order', 'ASC')
            ->order('id', 'ASC')
            ->selectResult();
    }
    
    /**
     * 分页查询广告
     */
    public function findWithPagination(array $conditions = [], int $page = 1, int $limit = 20): array
    {
        $bean = new PortalAdBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($conditions['ad_position'])) {
            $query->where('ad_position', $conditions['ad_position']);
        }
        if (!empty($conditions['ad_title'])) {
            $query->where('ad_title', 'like', '%' . $conditions['ad_title'] . '%');
        }
        if (isset($conditions['is_enabled'])) {
            $query->where('is_enabled', $conditions['is_enabled']);
        }
        if (!empty($conditions['ad_type'])) {
            $query->where('ad_type', $conditions['ad_type']);
        }
        
        // 获取总数
        $total = $query->count();
        
        // 分页查询
        $offset = ($page - 1) * $limit;
        $list = $query->order('ad_position', 'ASC')
            ->order('sort_order', 'ASC')
            ->order('id', 'DESC')
            ->limit($offset, $limit)
            ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
    
    /**
     * 创建广告
     */
    public function create(PortalAdBean $ad): PortalAdBean
    {
        // 验证数据
        $this->validateAd($ad);
        
        // 设置默认值
        if (is_null($ad->sortOrder)) {
            $ad->sortOrder = $this->getNextSortOrder($ad->adPosition);
        }
        if (is_null($ad->isEnabled)) {
            $ad->isEnabled = PortalAdBean::ENABLED_YES;
        }
        
        $bean = new PortalAdBean();
        $result = EntityManager::create($bean)
            ->insert([
                'ad_position' => $ad->adPosition,
                'ad_title' => $ad->adTitle,
                'ad_image' => $ad->adImage,
                'ad_url' => $ad->adUrl,
                'ad_content' => $ad->adContent,
                'ad_type' => $ad->adType,
                'width' => $ad->width,
                'height' => $ad->height,
                'sort_order' => $ad->sortOrder,
                'is_enabled' => $ad->isEnabled,
                'target_type' => $ad->targetType,
                'start_time' => $ad->startTime,
                'end_time' => $ad->endTime,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($result);
        }
        
        throw new ValidateException('创建广告失败');
    }
    
    /**
     * 更新广告
     */
    public function update(PortalAdBean $ad): PortalAdBean
    {
        if (!$ad->id) {
            throw new ValidateException('广告ID不能为空');
        }
        
        // 验证数据
        $this->validateAd($ad);
        
        $bean = new PortalAdBean();
        $result = EntityManager::create($bean)
            ->where('id', $ad->id)
            ->update([
                'ad_position' => $ad->adPosition,
                'ad_title' => $ad->adTitle,
                'ad_image' => $ad->adImage,
                'ad_url' => $ad->adUrl,
                'ad_content' => $ad->adContent,
                'ad_type' => $ad->adType,
                'width' => $ad->width,
                'height' => $ad->height,
                'sort_order' => $ad->sortOrder,
                'is_enabled' => $ad->isEnabled,
                'target_type' => $ad->targetType,
                'start_time' => $ad->startTime,
                'end_time' => $ad->endTime,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($ad->id);
        }
        
        throw new ValidateException('更新广告失败');
    }
    
    /**
     * 删除广告
     */
    public function delete(int $id): bool
    {
        $bean = new PortalAdBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->delete() > 0;
    }
    
    /**
     * 更新点击次数
     */
    public function updateClickCount(int $id): bool
    {
        $bean = new PortalAdBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->update(['click_count' => ['inc', 1]]) > 0;
    }
    
    /**
     * 更新展示次数
     */
    public function updateViewCount(int $id): bool
    {
        $bean = new PortalAdBean();
        return EntityManager::create($bean)
            ->where('id', $id)
            ->update(['view_count' => ['inc', 1]]) > 0;
    }
    
    /**
     * 获取广告位下一个排序号
     */
    private function getNextSortOrder(string $position): int
    {
        $bean = new PortalAdBean();
        $maxSort = EntityManager::create($bean)
            ->where('ad_position', $position)
            ->max('sort_order');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 验证广告数据
     */
    private function validateAd(PortalAdBean $ad): void
    {
        if (empty($ad->adPosition)) {
            throw new ValidateException('广告位标识不能为空');
        }
        if (empty($ad->adTitle)) {
            throw new ValidateException('广告标题不能为空');
        }
        if ($ad->adType === PortalAdBean::TYPE_IMAGE && empty($ad->adImage)) {
            throw new ValidateException('图片类型广告必须设置图片');
        }
        if ($ad->adType === PortalAdBean::TYPE_TEXT && empty($ad->adContent)) {
            throw new ValidateException('文字类型广告必须设置内容');
        }
        if ($ad->adType === PortalAdBean::TYPE_HTML && empty($ad->adContent)) {
            throw new ValidateException('HTML类型广告必须设置内容');
        }
    }
}
