<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 广告位 Bean
 * @Table(name="portal_ads")
 */
class PortalAdBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="ad_position", type="string")
     */
    public mixed $adPosition = null;

    /**
     * @Column(name="ad_title", type="string")
     */
    public mixed $adTitle = null;

    /**
     * @Column(name="ad_image", type="string")
     */
    public mixed $adImage = null;

    /**
     * @Column(name="ad_url", type="string")
     */
    public mixed $adUrl = null;

    /**
     * @Column(name="ad_content", type="string")
     */
    public mixed $adContent = null;

    /**
     * @Column(name="ad_type", type="string")
     */
    public mixed $adType = null;

    /**
     * @Column(name="width", type="integer")
     */
    public mixed $width = null;

    /**
     * @Column(name="height", type="integer")
     */
    public mixed $height = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="is_enabled", type="integer")
     */
    public mixed $isEnabled = null;

    /**
     * @Column(name="target_type", type="string")
     */
    public mixed $targetType = null;

    /**
     * @Column(name="start_time", type="datetime")
     */
    public mixed $startTime = null;

    /**
     * @Column(name="end_time", type="datetime")
     */
    public mixed $endTime = null;

    /**
     * @Column(name="click_count", type="integer")
     */
    public mixed $clickCount = null;

    /**
     * @Column(name="view_count", type="integer")
     */
    public mixed $viewCount = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;
    
    // 启用状态常量
    const ENABLED_YES = 1;
    const ENABLED_NO = 0;
    
    // 广告类型常量
    const TYPE_IMAGE = 'image';
    const TYPE_TEXT = 'text';
    const TYPE_HTML = 'html';
    
    // 链接打开方式常量
    const TARGET_SELF = '_self';
    const TARGET_BLANK = '_blank';
    

    
    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this->isEnabled === self::ENABLED_YES;
    }
    
    /**
     * 检查是否在有效期内
     */
    public function isInValidPeriod(): bool
    {
        $now = date('Y-m-d H:i:s');
        
        // 如果没有设置开始时间，认为已经开始
        $started = empty($this->startTime) || $this->startTime <= $now;
        
        // 如果没有设置结束时间，认为没有结束
        $notEnded = empty($this->endTime) || $this->endTime >= $now;
        
        return $started && $notEnded;
    }
    
    /**
     * 检查是否可以显示
     */
    public function canDisplay(): bool
    {
        return $this->isEnabled() && $this->isInValidPeriod();
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->clickCount++;
    }
    
    /**
     * 增加展示次数
     */
    public function incrementViewCount(): void
    {
        $this->viewCount++;
    }
    
    /**
     * 获取广告尺寸样式
     */
    public function getSizeStyle(): string
    {
        return "width: {$this->width}px; height: {$this->height}px;";
    }
    
    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'ad_position' => $this->adPosition,
            'ad_title' => $this->adTitle,
            'ad_image' => $this->adImage,
            'ad_url' => $this->adUrl,
            'ad_content' => $this->adContent,
            'ad_type' => $this->adType,
            'width' => $this->width,
            'height' => $this->height,
            'sort_order' => $this->sortOrder,
            'is_enabled' => $this->isEnabled,
            'target_type' => $this->targetType,
            'start_time' => $this->startTime,
            'end_time' => $this->endTime,
            'click_count' => $this->clickCount,
            'view_count' => $this->viewCount,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
