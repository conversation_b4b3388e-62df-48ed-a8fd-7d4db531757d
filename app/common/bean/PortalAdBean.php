<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\BaseBean;

/**
 * 广告位 Bean
 */
class PortalAdBean extends BaseBean
{
    // 表名
    protected string $table = 'portal_ads';
    
    // 主键
    protected string $primaryKey = 'id';
    
    // 字段定义
    public ?int $id = null;
    public string $adPosition = '';
    public string $adTitle = '';
    public string $adImage = '';
    public string $adUrl = '';
    public ?string $adContent = null;
    public string $adType = 'image';
    public int $width = 300;
    public int $height = 250;
    public int $sortOrder = 0;
    public int $isEnabled = 1;
    public string $targetType = '_blank';
    public ?string $startTime = null;
    public ?string $endTime = null;
    public int $clickCount = 0;
    public int $viewCount = 0;
    public ?string $createTime = null;
    public ?string $updateTime = null;
    
    // 字段映射
    protected array $fieldMap = [
        'id' => 'id',
        'adPosition' => 'ad_position',
        'adTitle' => 'ad_title',
        'adImage' => 'ad_image',
        'adUrl' => 'ad_url',
        'adContent' => 'ad_content',
        'adType' => 'ad_type',
        'width' => 'width',
        'height' => 'height',
        'sortOrder' => 'sort_order',
        'isEnabled' => 'is_enabled',
        'targetType' => 'target_type',
        'startTime' => 'start_time',
        'endTime' => 'end_time',
        'clickCount' => 'click_count',
        'viewCount' => 'view_count',
        'createTime' => 'create_time',
        'updateTime' => 'update_time',
    ];
    
    // 启用状态常量
    const ENABLED_YES = 1;
    const ENABLED_NO = 0;
    
    // 广告类型常量
    const TYPE_IMAGE = 'image';
    const TYPE_TEXT = 'text';
    const TYPE_HTML = 'html';
    
    // 链接打开方式常量
    const TARGET_SELF = '_self';
    const TARGET_BLANK = '_blank';
    
    /**
     * 获取字段映射
     */
    public function getFieldMap(): array
    {
        return $this->fieldMap;
    }
    
    /**
     * 获取表名
     */
    public function getTable(): string
    {
        return $this->table;
    }
    
    /**
     * 获取主键
     */
    public function getPrimaryKey(): string
    {
        return $this->primaryKey;
    }
    
    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this->isEnabled === self::ENABLED_YES;
    }
    
    /**
     * 检查是否在有效期内
     */
    public function isInValidPeriod(): bool
    {
        $now = date('Y-m-d H:i:s');
        
        // 如果没有设置开始时间，认为已经开始
        $started = empty($this->startTime) || $this->startTime <= $now;
        
        // 如果没有设置结束时间，认为没有结束
        $notEnded = empty($this->endTime) || $this->endTime >= $now;
        
        return $started && $notEnded;
    }
    
    /**
     * 检查是否可以显示
     */
    public function canDisplay(): bool
    {
        return $this->isEnabled() && $this->isInValidPeriod();
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->clickCount++;
    }
    
    /**
     * 增加展示次数
     */
    public function incrementViewCount(): void
    {
        $this->viewCount++;
    }
    
    /**
     * 获取广告尺寸样式
     */
    public function getSizeStyle(): string
    {
        return "width: {$this->width}px; height: {$this->height}px;";
    }
    
    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'ad_position' => $this->adPosition,
            'ad_title' => $this->adTitle,
            'ad_image' => $this->adImage,
            'ad_url' => $this->adUrl,
            'ad_content' => $this->adContent,
            'ad_type' => $this->adType,
            'width' => $this->width,
            'height' => $this->height,
            'sort_order' => $this->sortOrder,
            'is_enabled' => $this->isEnabled,
            'target_type' => $this->targetType,
            'start_time' => $this->startTime,
            'end_time' => $this->endTime,
            'click_count' => $this->clickCount,
            'view_count' => $this->viewCount,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
