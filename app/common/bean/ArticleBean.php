<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\mapping\Column;
use orm\mapping\Id;
use orm\mapping\Table;
use orm\mapping\Transient;
use orm\BaseBean;

/**
 * 文章Bean
 * @Table(name="articles")
 */
class ArticleBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="category_id", type="integer")
     */
    public mixed $categoryId = null;

    /**
     * @Column(name="title", type="string")
     */
    public mixed $title = null;

    /**
     * @Column(name="slug", type="string")
     */
    public mixed $slug = null;

    /**
     * @Column(name="summary", type="string")
     */
    public mixed $summary = null;

    /**
     * @Column(name="content", type="string")
     */
    public mixed $content = null;

    /**
     * @Column(name="author", type="string")
     */
    public mixed $author = null;

    /**
     * @Column(name="cover_image", type="string")
     */
    public mixed $coverImage = null;

    /**
     * @Column(name="status", type="string")
     */
    public mixed $status = null;

    /**
     * @Column(name="is_top", type="integer")
     */
    public mixed $isTop = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="view_count", type="integer")
     */
    public mixed $viewCount = null;

    /**
     * @Column(name="seo_title", type="string")
     */
    public mixed $seoTitle = null;

    /**
     * @Column(name="seo_keywords", type="string")
     */
    public mixed $seoKeywords = null;

    /**
     * @Column(name="seo_description", type="string")
     */
    public mixed $seoDescription = null;

    /**
     * @Column(name="publish_time", type="datetime")
     */
    public mixed $publishTime = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    /**
     * 自定义字段数据（非数据库字段）
     * @var mixed
     * @Transient
     */
    public mixed $customFields = null;

    // 文章状态常量
    const STATUS_DRAFT = 'draft';        // 草稿
    const STATUS_PUBLISHED = 'published'; // 已发布
    const STATUS_ARCHIVED = 'archived';   // 已归档

    // 置顶状态常量
    const TOP_NO = 0;  // 不置顶
    const TOP_YES = 1; // 置顶

    /**
     * 获取所有文章状态
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_ARCHIVED => '已归档'
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusText(): string
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取置顶状态文本
     */
    public function getTopText(): string
    {
        return $this->isTop ? '是' : '否';
    }

    /**
     * 检查是否已发布
     */
    public function isPublished(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 检查是否为草稿
     */
    public function isDraft(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * 检查是否已归档
     */
    public function isArchived(): bool
    {
        return $this->status === self::STATUS_ARCHIVED;
    }

    /**
     * 检查是否置顶
     */
    public function isTopArticle(): bool
    {
        return $this->isTop == self::TOP_YES;
    }

    /**
     * 验证文章状态
     */
    public function validateStatus(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_PUBLISHED, self::STATUS_ARCHIVED]);
    }

    /**
     * 获取摘要（如果没有摘要则从内容中截取）
     */
    public function getDisplaySummary(int $length = 200): string
    {
        if (!empty($this->summary)) {
            return $this->summary;
        }
        
        // 从内容中提取纯文本并截取
        $content = strip_tags($this->content ?? '');
        return mb_substr($content, 0, $length) . (mb_strlen($content) > $length ? '...' : '');
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->categoryId,
            'title' => $this->title,
            'slug' => $this->slug,
            'summary' => $this->summary,
            'content' => $this->content,
            'author' => $this->author,
            'cover_image' => $this->coverImage,
            'status' => $this->status,
            'status_text' => $this->getStatusText(),
            'is_top' => $this->isTop,
            'top_text' => $this->getTopText(),
            'sort_order' => $this->sortOrder,
            'view_count' => $this->viewCount,
            'seo_title' => $this->seoTitle,
            'seo_keywords' => $this->seoKeywords,
            'seo_description' => $this->seoDescription,
            'publish_time' => $this->publishTime,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
            'is_published' => $this->isPublished(),
            'is_draft' => $this->isDraft(),
            'is_archived' => $this->isArchived(),
            'is_top_article' => $this->isTopArticle(),
            'display_summary' => $this->getDisplaySummary()
        ];
    }
}
