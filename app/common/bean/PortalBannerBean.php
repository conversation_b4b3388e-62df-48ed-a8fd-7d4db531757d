<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 轮播Banner Bean
 * @Table(name="portal_banners")
 */
class PortalBannerBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="title", type="string")
     */
    public mixed $title = null;

    /**
     * @Column(name="image_url", type="string")
     */
    public mixed $imageUrl = null;

    /**
     * @Column(name="link_url", type="string")
     */
    public mixed $linkUrl = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="is_enabled", type="integer")
     */
    public mixed $isEnabled = null;

    /**
     * @Column(name="target_type", type="string")
     */
    public mixed $targetType = null;

    /**
     * @Column(name="start_time", type="datetime")
     */
    public mixed $startTime = null;

    /**
     * @Column(name="end_time", type="datetime")
     */
    public mixed $endTime = null;

    /**
     * @Column(name="click_count", type="integer")
     */
    public mixed $clickCount = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;
    
    // 启用状态常量
    const ENABLED_YES = 1;
    const ENABLED_NO = 0;
    
    // 链接打开方式常量
    const TARGET_SELF = '_self';
    const TARGET_BLANK = '_blank';
    

    
    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this->isEnabled === self::ENABLED_YES;
    }
    
    /**
     * 检查是否在有效期内
     */
    public function isInValidPeriod(): bool
    {
        $now = date('Y-m-d H:i:s');
        
        // 如果没有设置开始时间，认为已经开始
        $started = empty($this->startTime) || $this->startTime <= $now;
        
        // 如果没有设置结束时间，认为没有结束
        $notEnded = empty($this->endTime) || $this->endTime >= $now;
        
        return $started && $notEnded;
    }
    
    /**
     * 检查是否可以显示
     */
    public function canDisplay(): bool
    {
        return $this->isEnabled() && $this->isInValidPeriod();
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->clickCount++;
    }
    
    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'image_url' => $this->imageUrl,
            'link_url' => $this->linkUrl,
            'description' => $this->description,
            'sort_order' => $this->sortOrder,
            'is_enabled' => $this->isEnabled,
            'target_type' => $this->targetType,
            'start_time' => $this->startTime,
            'end_time' => $this->endTime,
            'click_count' => $this->clickCount,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
