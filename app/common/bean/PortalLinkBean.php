<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\Bean;

/**
 * 底部链接 Bean
 */
class PortalLinkBean extends Bean
{
    // 表名
    protected string $table = 'portal_links';
    
    // 主键
    protected string $primaryKey = 'id';
    
    // 字段定义
    public ?int $id = null;
    public string $categoryName = '';
    public string $linkTitle = '';
    public string $linkUrl = '';
    public string $description = '';
    public int $sortOrder = 0;
    public int $categorySort = 0;
    public int $isEnabled = 1;
    public string $targetType = '_blank';
    public int $isNofollow = 0;
    public int $clickCount = 0;
    public ?string $createTime = null;
    public ?string $updateTime = null;
    
    // 字段映射
    protected array $fieldMap = [
        'id' => 'id',
        'categoryName' => 'category_name',
        'linkTitle' => 'link_title',
        'linkUrl' => 'link_url',
        'description' => 'description',
        'sortOrder' => 'sort_order',
        'categorySort' => 'category_sort',
        'isEnabled' => 'is_enabled',
        'targetType' => 'target_type',
        'isNofollow' => 'is_nofollow',
        'clickCount' => 'click_count',
        'createTime' => 'create_time',
        'updateTime' => 'update_time',
    ];
    
    // 启用状态常量
    const ENABLED_YES = 1;
    const ENABLED_NO = 0;
    
    // 链接打开方式常量
    const TARGET_SELF = '_self';
    const TARGET_BLANK = '_blank';
    
    // nofollow常量
    const NOFOLLOW_YES = 1;
    const NOFOLLOW_NO = 0;
    
    /**
     * 获取字段映射
     */
    public function getFieldMap(): array
    {
        return $this->fieldMap;
    }
    
    /**
     * 获取表名
     */
    public function getTable(): string
    {
        return $this->table;
    }
    
    /**
     * 获取主键
     */
    public function getPrimaryKey(): string
    {
        return $this->primaryKey;
    }
    
    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this->isEnabled === self::ENABLED_YES;
    }
    
    /**
     * 检查是否需要nofollow
     */
    public function needNofollow(): bool
    {
        return $this->isNofollow === self::NOFOLLOW_YES;
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->clickCount++;
    }
    
    /**
     * 获取链接属性字符串
     */
    public function getLinkAttributes(): string
    {
        $attributes = [];
        
        if ($this->targetType === self::TARGET_BLANK) {
            $attributes[] = 'target="_blank"';
        }
        
        if ($this->needNofollow()) {
            $attributes[] = 'rel="nofollow"';
        }
        
        return implode(' ', $attributes);
    }
    
    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_name' => $this->categoryName,
            'link_title' => $this->linkTitle,
            'link_url' => $this->linkUrl,
            'description' => $this->description,
            'sort_order' => $this->sortOrder,
            'category_sort' => $this->categorySort,
            'is_enabled' => $this->isEnabled,
            'target_type' => $this->targetType,
            'is_nofollow' => $this->isNofollow,
            'click_count' => $this->clickCount,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
