<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\BaseBean;
use orm\mapping\Table;
use orm\mapping\Column;
use orm\mapping\Id;

/**
 * 底部链接 Bean
 * @Table(name="portal_links")
 */
class PortalLinkBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="category_name", type="string")
     */
    public mixed $categoryName = null;

    /**
     * @Column(name="link_title", type="string")
     */
    public mixed $linkTitle = null;

    /**
     * @Column(name="link_url", type="string")
     */
    public mixed $linkUrl = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="category_sort", type="integer")
     */
    public mixed $categorySort = null;

    /**
     * @Column(name="is_enabled", type="integer")
     */
    public mixed $isEnabled = null;

    /**
     * @Column(name="target_type", type="string")
     */
    public mixed $targetType = null;

    /**
     * @Column(name="is_nofollow", type="integer")
     */
    public mixed $isNofollow = null;

    /**
     * @Column(name="click_count", type="integer")
     */
    public mixed $clickCount = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;
    
    // 启用状态常量
    const ENABLED_YES = 1;
    const ENABLED_NO = 0;
    
    // 链接打开方式常量
    const TARGET_SELF = '_self';
    const TARGET_BLANK = '_blank';
    
    // nofollow常量
    const NOFOLLOW_YES = 1;
    const NOFOLLOW_NO = 0;
    

    
    /**
     * 检查是否启用
     */
    public function isEnabled(): bool
    {
        return $this->isEnabled === self::ENABLED_YES;
    }
    
    /**
     * 检查是否需要nofollow
     */
    public function needNofollow(): bool
    {
        return $this->isNofollow === self::NOFOLLOW_YES;
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClickCount(): void
    {
        $this->clickCount++;
    }
    
    /**
     * 获取链接属性字符串
     */
    public function getLinkAttributes(): string
    {
        $attributes = [];
        
        if ($this->targetType === self::TARGET_BLANK) {
            $attributes[] = 'target="_blank"';
        }
        
        if ($this->needNofollow()) {
            $attributes[] = 'rel="nofollow"';
        }
        
        return implode(' ', $attributes);
    }
    
    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'category_name' => $this->categoryName,
            'link_title' => $this->linkTitle,
            'link_url' => $this->linkUrl,
            'description' => $this->description,
            'sort_order' => $this->sortOrder,
            'category_sort' => $this->categorySort,
            'is_enabled' => $this->isEnabled,
            'target_type' => $this->targetType,
            'is_nofollow' => $this->isNofollow,
            'click_count' => $this->clickCount,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
        ];
    }
}
