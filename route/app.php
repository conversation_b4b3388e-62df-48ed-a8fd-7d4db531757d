<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 错误页面测试路由（仅在调试模式下可用）
if (app()->isDebug()) {
    Route::group('error-test', function () {
        Route::get('404', 'index/ErrorTest/test404');
        Route::get('500', 'index/ErrorTest/test500');
        Route::get('403', 'index/ErrorTest/test403');
        Route::get('general', 'index/ErrorTest/testGeneral');
        Route::any('ajax', 'index/ErrorTest/testAjax');
    });
}
