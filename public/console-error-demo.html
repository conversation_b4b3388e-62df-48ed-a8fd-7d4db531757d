<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器控制台错误输出演示</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .demo-container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .demo-btn { 
            display: inline-block; 
            padding: 12px 24px; 
            margin: 10px; 
            background: #1890ff; 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .demo-btn:hover { 
            background: #40a9ff; 
        }
        .demo-btn.danger { 
            background: #ff4d4f; 
        }
        .demo-btn.danger:hover { 
            background: #ff7875; 
        }
        .instructions { 
            background: #e6f7ff; 
            border: 1px solid #91d5ff; 
            border-radius: 6px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .console-preview { 
            background: #1e1e1e; 
            color: #d4d4d4; 
            padding: 20px; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            margin: 20px 0; 
            overflow-x: auto; 
        }
        .console-error { color: #f14c4c; }
        .console-log { color: #569cd6; }
        .console-group { color: #4ec9b0; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🐾 浏览器控制台错误输出演示</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>打开浏览器开发者工具</strong>：按 F12 或右键选择"检查元素"</li>
                <li><strong>切换到 Console 标签</strong>：查看控制台输出</li>
                <li><strong>点击下方按钮</strong>：触发不同类型的错误页面</li>
                <li><strong>观察控制台输出</strong>：查看详细的错误信息和堆栈跟踪</li>
            </ol>
        </div>

        <h3>🧪 测试错误页面</h3>
        <p>点击下面的按钮查看不同错误页面的控制台输出：</p>
        
        <a href="/test-console-error.php" class="demo-btn" target="_blank">
            🔧 调试模式错误页面
        </a>
        
        <button class="demo-btn" onclick="simulateError('404')">
            📄 模拟404错误
        </button>
        
        <button class="demo-btn" onclick="simulateError('500')">
            ⚠️ 模拟500错误
        </button>
        
        <button class="demo-btn danger" onclick="triggerJSError()">
            💥 触发JS错误
        </button>

        <h3>📺 控制台输出预览</h3>
        <p>当错误页面加载时，控制台将显示类似以下的信息：</p>
        
        <div class="console-preview">
<span class="console-group">🚨 系统错误详情</span>
<span class="console-error">  错误代码: 500</span>
<span class="console-error">  错误标题: 测试控制台输出</span>
<span class="console-error">  错误信息: 这是一个测试错误页面，请打开浏览器控制台查看详细信息</span>
<span class="console-error">  发生时间: 2025-07-15 09:50:23</span>
<span class="console-error">  页面URL: http://news.test.jijiaox.com/test-console-error.php</span>
<span class="console-error">  用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</span>
<span class="console-group">  🔍 调试信息</span>
<span class="console-error">    错误文件: /path/to/file.php 第 14 行</span>
<span class="console-error">    请求URL: http://news.test.jijiaox.com/test-console-error.php</span>
<span class="console-error">    请求方法: GET</span>
<span class="console-group">    📋 堆栈跟踪</span>
<span class="console-log">      #0 {main}</span>
<span class="console-log">💥 发生了一个错误！</span>
<span class="console-log">请查看上方的错误详情进行调试</span>
<span class="console-log">🔧 调试模式已开启，详细信息已输出到控制台</span>
        </div>

        <div class="instructions">
            <h3>✨ 功能特点</h3>
            <ul>
                <li><strong>🎯 详细错误信息</strong>：包含错误代码、标题、描述等</li>
                <li><strong>🔍 调试信息</strong>：文件路径、行号、堆栈跟踪（仅调试模式）</li>
                <li><strong>🌐 请求上下文</strong>：URL、请求方法、用户代理等</li>
                <li><strong>🎨 样式化输出</strong>：使用颜色和图标美化控制台显示</li>
                <li><strong>🔒 安全考虑</strong>：生产模式下隐藏敏感调试信息</li>
            </ul>
        </div>
    </div>

    <script>
        function simulateError(type) {
            // 模拟不同类型的错误
            const errorData = {
                '404': {
                    code: '404',
                    title: '页面不存在',
                    message: '抱歉，您访问的页面不存在或已被删除'
                },
                '500': {
                    code: '500',
                    title: '服务器内部错误',
                    message: '抱歉，服务器遇到了内部错误，无法完成您的请求'
                }
            };
            
            const error = errorData[type];
            
            console.group('🚨 模拟错误详情');
            console.error('错误代码:', error.code);
            console.error('错误标题:', error.title);
            console.error('错误信息:', error.message);
            console.error('发生时间:', new Date().toLocaleString());
            console.error('页面URL:', window.location.href);
            console.groupEnd();
            
            console.log('%c💥 模拟错误已触发！', 'color: #ff4757; font-size: 16px; font-weight: bold;');
            console.log('%c这是一个演示，实际错误页面会显示更多详细信息', 'color: #747d8c; font-size: 12px;');
        }
        
        function triggerJSError() {
            console.log('%c⚠️ 即将触发JavaScript错误...', 'color: #ff6b6b; font-size: 14px;');
            setTimeout(() => {
                // 故意触发一个错误
                nonExistentFunction();
            }, 1000);
        }
        
        // 页面加载时的提示
        console.log('%c🎉 错误输出演示页面已加载', 'color: #2ed573; font-size: 16px; font-weight: bold;');
        console.log('%c请点击页面上的按钮来测试不同的错误输出效果', 'color: #747d8c; font-size: 12px;');
    </script>
</body>
</html>
