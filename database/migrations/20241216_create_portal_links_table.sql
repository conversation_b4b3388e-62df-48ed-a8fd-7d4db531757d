-- 创建底部链接表
CREATE TABLE IF NOT EXISTS `portal_links` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `link_title` varchar(255) NOT NULL DEFAULT '' COMMENT '链接标题',
  `link_url` varchar(500) NOT NULL DEFAULT '' COMMENT '链接URL',
  `description` varchar(500) DEFAULT '' COMMENT '链接描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `category_sort` int(11) NOT NULL DEFAULT '0' COMMENT '分类排序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `target_type` enum('_self','_blank') NOT NULL DEFAULT '_blank' COMMENT '链接打开方式',
  `is_nofollow` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否添加nofollow：1-是，0-否',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_name` (`category_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_category_sort` (`category_sort`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='底部链接表';

-- 插入示例数据
INSERT INTO `portal_links` (`category_name`, `link_title`, `link_url`, `description`, `sort_order`, `category_sort`, `is_enabled`, `target_type`) VALUES
('友情链接', '百度', 'https://www.baidu.com', '百度搜索引擎', 1, 1, 1, '_blank'),
('友情链接', '腾讯', 'https://www.tencent.com', '腾讯官网', 2, 1, 1, '_blank'),
('友情链接', '阿里巴巴', 'https://www.alibaba.com', '阿里巴巴集团', 3, 1, 1, '_blank'),
('合作伙伴', '新浪', 'https://www.sina.com.cn', '新浪网', 1, 2, 1, '_blank'),
('合作伙伴', '搜狐', 'https://www.sohu.com', '搜狐网', 2, 2, 1, '_blank'),
('政府机构', '中国政府网', 'http://www.gov.cn', '中华人民共和国中央人民政府门户网站', 1, 3, 1, '_blank'),
('政府机构', '新华网', 'http://www.xinhuanet.com', '新华通讯社官网', 2, 3, 1, '_blank');
