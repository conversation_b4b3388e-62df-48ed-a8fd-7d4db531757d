-- 创建轮播banner表
CREATE TABLE IF NOT EXISTS `portal_banners` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL DEFAULT '' COMMENT 'Banner标题',
  `image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '图片URL',
  `link_url` varchar(500) NOT NULL DEFAULT '' COMMENT '链接URL',
  `description` text COMMENT 'Banner描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `target_type` enum('_self','_blank') NOT NULL DEFAULT '_self' COMMENT '链接打开方式',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播Banner表';

-- 插入示例数据
INSERT INTO `portal_banners` (`title`, `image_url`, `link_url`, `description`, `sort_order`, `is_enabled`, `target_type`) VALUES
('欢迎访问新闻门户', '/static/images/banner1.jpg', '/', '这是一个示例轮播图', 1, 1, '_self'),
('最新资讯', '/static/images/banner2.jpg', '/articles', '查看最新资讯内容', 2, 1, '_self'),
('热门话题', '/static/images/banner3.jpg', '/category/1', '关注热门话题讨论', 3, 1, '_self');
