-- 创建广告位表
CREATE TABLE IF NOT EXISTS `portal_ads` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_position` varchar(50) NOT NULL DEFAULT '' COMMENT '广告位标识（如：index-1, index-2等）',
  `ad_title` varchar(255) NOT NULL DEFAULT '' COMMENT '广告标题',
  `ad_image` varchar(500) NOT NULL DEFAULT '' COMMENT '广告图片URL',
  `ad_url` varchar(500) NOT NULL DEFAULT '' COMMENT '广告链接URL',
  `ad_content` text COMMENT '广告内容/描述',
  `ad_type` enum('image','text','html') NOT NULL DEFAULT 'image' COMMENT '广告类型：image-图片，text-文字，html-HTML代码',
  `width` int(11) NOT NULL DEFAULT '300' COMMENT '广告宽度（像素）',
  `height` int(11) NOT NULL DEFAULT '250' COMMENT '广告高度（像素）',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `target_type` enum('_self','_blank') NOT NULL DEFAULT '_blank' COMMENT '链接打开方式',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '展示次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_ad_position` (`ad_position`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告位表';

-- 插入示例数据
INSERT INTO `portal_ads` (`ad_position`, `ad_title`, `ad_image`, `ad_url`, `ad_content`, `ad_type`, `width`, `height`, `sort_order`, `is_enabled`, `target_type`) VALUES
('index-1', '首页顶部广告', '/static/images/ad1.jpg', 'https://example.com/ad1', '这是首页顶部广告位', 'image', 728, 90, 1, 1, '_blank'),
('index-2', '首页侧边广告', '/static/images/ad2.jpg', 'https://example.com/ad2', '这是首页侧边广告位', 'image', 300, 250, 1, 1, '_blank'),
('index-3', '首页底部广告', '/static/images/ad3.jpg', 'https://example.com/ad3', '这是首页底部广告位', 'image', 960, 120, 1, 1, '_blank');
